# ESP32 MCP Wake 完整使用说明

## 系统概述

ESP32 MCP Wake是一个智能语音唤醒和设备间通信系统，支持定时任务管理、多设备管理和设备间智能通信。

## 主要功能

### 1. 设备管理
- **添加设备**: 支持添加多个ESP32设备到系统中
- **设备状态监控**: 实时显示设备在线状态、工作状态等
- **设备通信**: 支持设备间消息发送和中转通信

### 2. 定时任务管理
- **绝对时间任务**: 设置特定时间执行的任务（如每天8:00）
- **相对时间任务**: 设置延时执行的任务（如5分钟后）
- **一次性任务**: 只执行一次的任务
- **任务控制**: 启用/禁用、删除任务

### 3. 设备间通信
- **语音命令触发**: 通过语音命令触发设备间通信
- **中转通信**: 通过Web服务器中转消息到目标设备
- **直接通信**: 设备间直接点对点通信
- **智能解析**: 自动解析语音命令中的目标设备和消息内容

## 使用方法

### 启动系统

1. **启动Web服务器**:
   ```bash
   cd web
   python server.py
   ```

2. **访问Web界面**:
   打开浏览器访问 `http://localhost:5000`

### 设备管理

#### 添加设备
1. 在Web界面找到"设备管理"部分
2. 填写设备信息：
   - 设备ID: 唯一标识符（如device2）
   - 设备名称: 显示名称（如设备2）
   - IP地址: 设备的IP地址（如************）
   - 端口: 设备端口（默认8081）
3. 点击"添加设备"按钮
4. 系统会自动刷新设备列表

#### 设备状态监控
- 绿色圆点: 设备在线
- 红色圆点: 设备离线
- 状态标签: 显示设备当前状态（空闲/监听中/讲话中等）

### 定时任务管理

#### 添加绝对时间任务
1. 选择"绝对时间"选项卡
2. 设置时间（时:分:秒）
3. 选择重复日期（周日到周六）
4. 输入消息内容
5. 可选择"一次性任务"
6. 点击"添加定时消息"
7. 任务列表会自动刷新显示新任务

#### 添加相对时间任务
1. 选择"相对时间"选项卡
2. 设置时间间隔（小时:分钟:秒）
3. 输入消息内容
4. 可选择"一次性任务"
5. 点击"添加定时消息"
6. 任务列表会自动刷新显示新任务

#### 任务管理
- **暂停/启用**: 点击任务右侧的暂停/播放按钮
- **删除任务**: 点击垃圾桶图标，确认后删除
- **查看详情**: 任务卡片显示下次执行时间、最后执行时间等信息

### 设备间通信

#### 方式一：Web界面操作
1. 在"设备间通信"部分选择源设备和目标设备
2. 输入要发送的消息
3. 可选择"直接回复"模式（使用`,,,`分隔符格式）
4. 选择通信方式：
   - **中转模式**: 通过Web服务器中转
   - **直接通信**: 设备间直接连接
5. 点击"发送消息"

#### 方式二：语音命令触发（推荐）
对任意设备说话，使用以下格式：
- "告诉设备2该去开会了"
- "通知客厅设备播放音乐"
- "让卧室设备关灯睡觉"

系统会自动：
1. 解析语音命令中的目标设备
2. 提取要发送的消息内容
3. 通过Web服务器API中转消息到目标设备

#### 直接回复模式
使用`,,,`分隔符可以让目标设备只回复特定内容：
```
"请你只回复,,,现在几点了,,,之间的内容，不要添加任何其他文字。"
```

## 技术架构

### 系统组件
1. **ESP32设备**: 语音识别和处理
2. **Web服务器**: 设备管理和消息中转
3. **Web界面**: 用户操作界面
4. **数据存储**: JSON文件存储设备和任务信息

### 通信流程
```
用户语音 → ESP32设备 → 语音命令解析 → Web服务器API → 目标设备
```

### API接口
- `GET /api/devices`: 获取设备列表
- `POST /api/devices`: 添加设备
- `DELETE /api/devices/{id}`: 删除设备
- `POST /api/devices/send-between`: 设备间中转通信
- `POST /api/devices/voice-command`: 语音命令处理
- `GET /api/timers`: 获取任务列表
- `POST /api/timers`: 添加绝对时间任务
- `POST /api/timers/relative`: 添加相对时间任务

## 配置说明

### 默认配置
- Web服务器端口: 5000
- ESP32默认端口: 8081
- 设备数据文件: `devices.json`
- 任务数据文件: `tasks.json`

### 网络配置
确保所有ESP32设备在同一网络中，并且可以相互访问。

## 故障排除

### 常见问题

1. **设备显示离线**
   - 检查设备IP地址是否正确
   - 确认设备在同一网络中
   - 检查设备是否正常运行

2. **语音命令无响应**
   - 确认ESP32设备正常工作
   - 检查语音命令格式是否正确
   - 查看Web服务器日志

3. **任务不执行**
   - 检查任务是否启用
   - 确认时间设置正确
   - 查看任务管理器状态

4. **设备间通信失败**
   - 确认目标设备在线
   - 检查网络连接
   - 尝试使用中转模式

### 日志查看
Web服务器会在控制台输出详细日志，包括：
- 设备连接状态
- 任务执行情况
- API调用记录
- 错误信息

## 扩展功能

### 添加新设备
1. 确保新设备运行相同的ESP32固件
2. 在Web界面添加设备信息
3. 测试设备连接和通信

### 自定义语音命令
可以修改ESP32代码中的语音命令解析逻辑，支持更多命令格式。

### 集成其他服务
Web服务器提供REST API，可以轻松集成到其他系统中。

## 安全注意事项

1. **网络安全**: 确保设备在安全的内网环境中
2. **访问控制**: 生产环境建议添加身份验证
3. **数据备份**: 定期备份设备和任务配置文件

## 更新日志

### v2.0 (当前版本)
- ✅ 添加设备间智能通信功能
- ✅ 支持语音命令触发设备通信
- ✅ 优化任务管理，添加后自动刷新
- ✅ 改进中转通信机制
- ✅ 增强Web界面用户体验

### v1.0
- ✅ 基础定时任务功能
- ✅ ESP32设备管理
- ✅ Web管理界面

## 技术支持

如有问题或建议，请查看：
1. 系统日志输出
2. 设备状态监控
3. API响应信息

---

**ESP32 MCP Wake** - 智能语音唤醒与设备间通信系统 