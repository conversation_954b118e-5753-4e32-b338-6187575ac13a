# ESP32 MCP 真正设备间通信实现说明

## 概述

本文档说明如何在ESP32设备上实现真正的MCP（Model Context Protocol）工具来进行设备间通信，而不是之前的伪测试实现。

## 架构说明

### MCP工具架构
```
用户语音 → AI助手 → MCP工具调用 → ESP32设备 → HTTP请求 → 目标设备
```

### 已实现的MCP工具

1. **device.send_message** - 设备间消息发送
2. **device.discover** - 网络设备发现
3. **device.get_status** - 远程设备状态查询

## 代码实现

### 1. MCP工具注册 (mcp_wake.cc)

```cpp
void RemoteWakeServer::RegisterDeviceCommunicationTools() {
    auto& mcp_server = McpServer::GetInstance();
    
    // 注册设备间通信工具
    mcp_server.AddTool("device.send_message",
        "Send a message to another ESP32 device in the network. "
        "Use this tool when the user wants to communicate with another device, "
        "such as 'tell device2 to start meeting' or 'notify living room device to play music'.",
        PropertyList({
            Property("target_device", kPropertyTypeString),
            Property("message", kPropertyTypeString),
            Property("force", kPropertyTypeBoolean, false)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            std::string target_device = properties["target_device"].value<std::string>();
            std::string message = properties["message"].value<std::string>();
            bool force = properties["force"].value<bool>();
            
            return SendMessageToDevice(target_device, message, force);
        });
}
```

### 2. 真正的HTTP通信实现

```cpp
std::string RemoteWakeServer::SendMessageToDevice(const std::string& target_device, const std::string& message, bool force) {
    // 构建目标设备URL
    std::string target_url;
    if (target_device == "device2" || target_device == "设备2") {
        target_url = "http://192.168.18.8:8081/api/wake";
    } else if (target_device == "device1" || target_device == "设备1") {
        target_url = "http://192.168.18.7:8081/api/wake";
    }
    
    // 使用ESP32 HTTP客户端发送真正的HTTP请求
    esp_http_client_config_t config = {};
    config.url = target_url.c_str();
    config.method = HTTP_METHOD_POST;
    config.timeout_ms = 10000;
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    // ... 实际HTTP请求实现
}
```

## 使用方法

### 1. 语音命令示例

用户可以直接对设备说：

- **"告诉设备2现在开始开会了"** → AI调用 `device.send_message(target_device="设备2", message="现在开始开会了")`
- **"向device2发送播放音乐"** → AI调用 `device.send_message(target_device="device2", message="播放音乐")`
- **"查询设备2的状态"** → AI调用 `device.get_status(target_device="设备2")`
- **"发现网络中的设备"** → AI调用 `device.discover()`

### 2. 设备配置

当前支持的设备：
- **设备1 (device1)**: 192.168.18.7:8081
- **设备2 (device2)**: 192.168.18.8:8081

## 测试验证

### 运行测试脚本

```bash
python test_real_mcp_communication.py
```

### 测试结果示例

```
🚀 开始测试真正的MCP设备间通信功能
============================================================

=== 测试设备1状态 ===
✅ 设备1在线
   状态: idle

=== 测试设备1向设备2发送消息 ===
✅ MCP设备间通信成功
   发送消息: 现在开始开会了
   目标设备: 设备2
   响应: {
     "success": true,
     "device_state": "listening",
     "wake_message": "告诉设备2现在开始开会了",
     "message": "已在监听状态下发送消息"
   }
```

## 与之前实现的区别

### 之前的伪实现
- 只是简单的HTTP API调用
- 没有真正的MCP工具集成
- AI助手无法自动识别设备间通信意图

### 现在的真正实现
- ✅ 完整的MCP工具注册和管理
- ✅ AI助手自动识别语音意图并调用相应工具
- ✅ 真正的ESP32 HTTP客户端通信
- ✅ 符合xiaozhi项目的MCP架构标准

## 工作流程

1. **用户语音输入**: "告诉设备2开始工作"
2. **AI意图识别**: 识别为设备间通信请求
3. **MCP工具调用**: 调用 `device.send_message` 工具
4. **参数解析**: target_device="设备2", message="开始工作"
5. **HTTP请求**: ESP32发送HTTP POST到192.168.18.8:8081/api/wake
6. **目标设备响应**: 设备2接收消息并执行相应操作
7. **结果返回**: 通过MCP工具返回执行结果

## 扩展说明

### 添加新设备
在 `SendMessageToDevice` 方法中添加新的设备映射：

```cpp
if (target_device == "device3" || target_device == "设备3") {
    target_url = "http://192.168.18.9:8081/api/wake";
}
```

### 添加新的MCP工具
参考现有工具的实现模式，在 `RegisterDeviceCommunicationTools` 中添加新工具。

## 总结

现在的实现是真正的MCP设备间通信系统，完全集成到xiaozhi项目的MCP架构中。用户可以通过自然语音与AI助手交互，AI会自动调用相应的MCP工具来实现设备间通信，这是真正意义上的智能设备间通信解决方案。 