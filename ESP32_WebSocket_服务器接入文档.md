# ESP32 WebSocket 服务器接入文档

## 概述

ESP32设备已改为WebSocket客户端架构，主动连接公网服务器 `ws://106.14.123.12:5000/socket.io/`。
服务器端需要支持Socket.IO协议，处理设备注册、状态管理、消息转发等功能。

## 服务器要求

### 基础要求
- **协议**: Socket.IO v4.x 兼容
- **端口**: 5000
- **传输**: WebSocket (transport=websocket)
- **路径**: `/socket.io/?EIO=4&transport=websocket`

### 技术栈建议
- **Node.js**: Socket.IO 官方支持
- **Python**: python-socketio
- **其他**: 任何支持Socket.IO v4的框架

## Socket.IO 消息格式

### 消息结构
```
42["event_name", data]
```
- `42`: Socket.IO数据包类型（事件消息）
- `event_name`: 事件名称字符串
- `data`: JSON对象或字符串

### 心跳机制
- **Ping**: `"2"` (ESP32发送)
- **Pong**: `"3"` (服务器响应)
- **间隔**: 30秒

## 必须处理的事件

### 1. 设备注册 (device_register)

**ESP32发送:**
```json
42["device_register", {
  "device_id": "192_168_18_43",
  "device_name": "主显示设备",
  "device_type": "esp32",
  "capabilities": "voice,display"
}]
```

**服务器响应:**
```json
42["device_register_response", {
  "success": true,
  "message": "Device registered successfully"
}]
```

### 2. 设备状态上报 (device_status)

**ESP32发送:**
```json
42["device_status", {
  "device_id": "192_168_18_43",
  "device_name": "主显示设备",
  "device_state": "idle",
  "state_code": 1,
  "connected": true,
  "registered": true,
  "can_speak": true,
  "voice_detected": false,
  "timestamp": 1704067200
}]
```

### 3. TTS播放指令 (speak_message)

**服务器发送:**
```json
42["speak_message", {
  "message": "你好，这是一条测试消息",
  "force": false
}]
```

**ESP32响应:**
```json
42["speak_response", {
  "success": true,
  "message": "Message processed successfully",
  "device_state": "speaking"
}]
```

### 4. 设备间通信转发 (device_message_forward)

**ESP32A发送:**
```json
42["device_message_forward", {
  "source_device": "192_168_18_43",
  "target_device": "192_168_18_46",
  "message": "告诉客厅设备播放音乐",
  "force": false
}]
```

**服务器转发给ESP32B:**
```json
42["device_communication", {
  "target_device": "192_168_18_46",
  "message": "告诉客厅设备播放音乐",
  "force": false
}]
```

### 5. 设备列表查询 (device_list_request)

**ESP32发送:**
```json
42["device_list_request", {}]
```

**服务器响应:**
```json
42["device_list_response", {
  "success": true,
  "devices": [
    {
      "device_id": "192_168_18_43",
      "device_name": "主显示设备",
      "device_type": "esp32",
      "online": true,
      "last_seen": 1704067200
    },
    {
      "device_id": "192_168_18_46", 
      "device_name": "客厅设备",
      "device_type": "esp32",
      "online": false,
      "last_seen": 1704067100
    }
  ]
}]
```

### 6. 设备状态查询 (device_status_request)

**ESP32发送:**
```json
42["device_status_request", {
  "target_device": "192_168_18_46"
}]
```

**服务器响应:**
```json
42["device_status_response", {
  "success": true,
  "target_device": "192_168_18_46",
  "status": {
    "device_state": "idle",
    "can_speak": true,
    "online": true
  }
}]
```

## 服务器端实现要点

### 1. 连接管理
```python
# 伪代码示例
connected_devices = {}  # device_id -> socket_id mapping

@socketio.on('connect')
def handle_connect():
    print(f'ESP32 connected: {request.sid}')

@socketio.on('disconnect')
def handle_disconnect():
    # 清理设备注册信息
    remove_device_by_socket(request.sid)
```

### 2. 设备注册处理
```python
@socketio.on('device_register')
def handle_device_register(data):
    device_id = data.get('device_id')
    device_name = data.get('device_name')
    
    # 存储设备信息
    connected_devices[device_id] = {
        'socket_id': request.sid,
        'device_name': device_name,
        'device_type': data.get('device_type'),
        'capabilities': data.get('capabilities'),
        'last_seen': time.time(),
        'online': True
    }
    
    # 响应注册成功
    emit('device_register_response', {
        'success': True,
        'message': 'Device registered successfully'
    })
```

### 3. 消息转发处理
```python
@socketio.on('device_message_forward')
def handle_message_forward(data):
    source_device = data.get('source_device')
    target_device = data.get('target_device')
    message = data.get('message')
    force = data.get('force', False)
    
    # 查找目标设备
    if target_device in connected_devices:
        target_socket = connected_devices[target_device]['socket_id']
        
        # 转发消息到目标设备
        emit('device_communication', {
            'target_device': target_device,
            'message': message,
            'force': force
        }, room=target_socket)
        
        # 通知源设备转发成功
        emit('message_forward_response', {
            'success': True,
            'message': 'Message forwarded successfully'
        })
    else:
        # 目标设备离线
        emit('message_forward_response', {
            'success': False,
            'message': f'Target device {target_device} not found'
        })
```

### 4. TTS播放控制
```python
@socketio.on('admin_speak_command')  # 来自管理界面
def handle_admin_speak(data):
    target_device = data.get('target_device')
    message = data.get('message')
    force = data.get('force', False)
    
    if target_device in connected_devices:
        target_socket = connected_devices[target_device]['socket_id']
        
        emit('speak_message', {
            'message': message,
            'force': force
        }, room=target_socket)
```

### 5. 心跳处理
```python
@socketio.on('message')  # 原始消息处理
def handle_raw_message(data):
    if data == '2':  # Socket.IO ping
        emit('message', '3')  # 响应pong
        
        # 更新设备最后活跃时间
        device_id = get_device_id_by_socket(request.sid)
        if device_id:
            connected_devices[device_id]['last_seen'] = time.time()
```

## 部署配置

### Node.js 示例服务器
```javascript
const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const connectedDevices = new Map();

io.on('connection', (socket) => {
  console.log('ESP32 connected:', socket.id);
  
  socket.on('device_register', (data) => {
    console.log('Device register:', data);
    connectedDevices.set(data.device_id, {
      socketId: socket.id,
      ...data,
      lastSeen: Date.now()
    });
    
    socket.emit('device_register_response', {
      success: true,
      message: 'Device registered successfully'
    });
  });
  
  socket.on('device_message_forward', (data) => {
    const targetDevice = connectedDevices.get(data.target_device);
    if (targetDevice) {
      io.to(targetDevice.socketId).emit('device_communication', data);
    }
  });
  
  socket.on('disconnect', () => {
    // 清理断开的设备
    for (const [deviceId, device] of connectedDevices) {
      if (device.socketId === socket.id) {
        connectedDevices.delete(deviceId);
        break;
      }
    }
  });
});

server.listen(5000, '0.0.0.0', () => {
  console.log('Socket.IO server running on port 5000');
});
```

### Python Flask-SocketIO 示例
```python
from flask import Flask
from flask_socketio import SocketIO, emit
import time

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

connected_devices = {}

@socketio.on('connect')
def handle_connect():
    print(f'ESP32 connected: {request.sid}')

@socketio.on('device_register')
def handle_device_register(data):
    device_id = data.get('device_id')
    connected_devices[device_id] = {
        'socket_id': request.sid,
        **data,
        'last_seen': time.time(),
        'online': True
    }
    
    emit('device_register_response', {
        'success': True,
        'message': 'Device registered successfully'
    })

@socketio.on('device_message_forward')
def handle_message_forward(data):
    target_device = data.get('target_device')
    if target_device in connected_devices:
        target_socket = connected_devices[target_device]['socket_id']
        emit('device_communication', data, room=target_socket)

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
```

## 管理接口建议

### REST API 补充
```python
# 获取设备列表
@app.route('/api/devices', methods=['GET'])
def get_devices():
    return jsonify({
        'devices': list(connected_devices.values())
    })

# 发送TTS指令
@app.route('/api/devices/<device_id>/speak', methods=['POST'])
def send_speak_command(device_id):
    data = request.json
    message = data.get('message')
    force = data.get('force', False)
    
    if device_id in connected_devices:
        socket_id = connected_devices[device_id]['socket_id']
        socketio.emit('speak_message', {
            'message': message,
            'force': force
        }, room=socket_id)
        
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': 'Device not found'})
```

## 测试验证

### 1. 连接测试
- ESP32启动后应能成功连接到服务器
- 设备注册事件应能正确处理
- 心跳包应能正常收发

### 2. 功能测试
- TTS播放指令能否正确下发
- 设备间消息转发是否正常
- 设备状态查询是否响应

### 3. 异常处理
- 设备断线重连机制
- 消息发送失败处理
- 服务器重启后设备重连

## 注意事项

1. **安全性**: 建议添加设备认证机制
2. **性能**: 大量设备时需要考虑负载均衡
3. **可靠性**: 实现消息确认和重试机制
4. **监控**: 添加设备在线状态监控和日志
5. **扩展性**: 支持设备分组和批量操作

## 常见问题

### Q: 如何处理设备ID冲突？
A: 建议使用MAC地址或UUID作为设备ID，确保唯一性。

### Q: 消息丢失如何处理？
A: 实现消息确认机制，超时重发。

### Q: 如何支持离线消息？
A: 服务器端缓存离线消息，设备上线后推送。

### Q: 性能优化建议？
A: 使用Redis存储设备状态，支持集群部署。 