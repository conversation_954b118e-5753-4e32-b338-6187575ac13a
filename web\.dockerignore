# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README*.md
*.md

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# SSL certificates (will be mounted)
ssl/

# Environment files
.env*
!.env.production

# Deployment scripts
deploy.sh
tunnel_deploy.py

# Test files
test_*
*_test.py
