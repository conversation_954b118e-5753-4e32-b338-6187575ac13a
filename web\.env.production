# ESP32 MCP Wake Web管理界面 - 生产环境配置

# Flask环境
FLASK_ENV=production
FLASK_DEBUG=False

# ESP32设备配置
ESP32_IP=*************
ESP32_PORT=8081
ESP32_TIMEOUT=10

# Web服务器配置
WEB_HOST=0.0.0.0
WEB_PORT=5000

# 安全配置
SECRET_KEY=esp32-mcp-wake-production-secret-key-2025-change-this-in-production

# 数据文件
TASKS_FILE=timer_tasks.json
DEVICES_FILE=devices.json

# 任务检查间隔
CHECK_INTERVAL=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/esp32_web.log

# 数据库配置 (如果将来需要)
# DATABASE_URL=sqlite:///esp32_wake.db

# 外部服务配置 (如果需要)
# REDIS_URL=redis://localhost:6379/0
# CELERY_BROKER_URL=redis://localhost:6379/0
