# 🚀 ESP32 MCP Wake Web - 公网部署快速开始

## 选择部署方案

### 🎯 方案1: 内网穿透 (最快，适合测试)

**适用场景**: 快速测试、演示、个人使用

```bash
# 1. 安装ngrok (如果未安装)
# Windows: choco install ngrok
# macOS: brew install ngrok  
# Linux: snap install ngrok

# 2. 运行部署脚本
cd web
python tunnel_deploy.py
```

**优点**: 
- ✅ 5分钟内完成部署
- ✅ 无需服务器
- ✅ 自动获取公网地址

**缺点**: 
- ❌ 每次重启URL会变化
- ❌ 免费版有连接限制
- ❌ 依赖本地网络稳定性

---

### 🐳 方案2: Docker部署 (推荐，适合生产)

**适用场景**: 生产环境、长期使用、多用户访问

```bash
# 1. 准备服务器 (VPS/云服务器)
# 最低配置: 1GB RAM, 1 CPU核心

# 2. 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 3. 克隆项目
git clone <your-repo-url>
cd testawake/web

# 4. 配置环境变量
cp .env.production .env
nano .env  # 修改ESP32_IP等配置

# 5. 一键部署
chmod +x deploy.sh
./deploy.sh
```

**优点**: 
- ✅ 稳定可靠
- ✅ 支持HTTPS
- ✅ 自动重启
- ✅ 完整的生产环境

**缺点**: 
- ❌ 需要服务器
- ❌ 需要一定技术基础

---

### ☁️ 方案3: 云平台部署 (最简单，适合新手)

**适用场景**: 零运维、自动扩展、全球访问

#### Railway 部署 (推荐)

```bash
# 1. 访问 https://railway.app
# 2. 连接GitHub账号
# 3. 导入项目仓库
# 4. 设置环境变量:
#    ESP32_IP = *************
#    SECRET_KEY = (自动生成)
# 5. 点击部署
```

#### Render 部署

```bash
# 1. 访问 https://render.com
# 2. 连接GitHub账号  
# 3. 选择Web Service
# 4. 导入项目仓库
# 5. 自动检测配置并部署
```

**优点**: 
- ✅ 零配置部署
- ✅ 自动HTTPS
- ✅ 全球CDN
- ✅ 自动备份

**缺点**: 
- ❌ 免费版有限制
- ❌ 可能需要付费

---

## 🔧 配置说明

### 必需配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `ESP32_IP` | ESP32设备IP地址 | `*************` |
| `SECRET_KEY` | Flask安全密钥 | 随机字符串 |

### 可选配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `ESP32_PORT` | ESP32设备端口 | `8081` |
| `WEB_PORT` | Web服务端口 | `5000` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

---

## 🔍 部署后验证

### 1. 健康检查
```bash
curl http://your-domain.com/api/health
```

### 2. 功能测试
- ✅ 访问Web界面
- ✅ 查看设备状态  
- ✅ 发送测试消息
- ✅ 管理定时任务

### 3. 性能监控
```bash
# Docker部署
docker-compose logs -f

# 云平台部署
# 查看平台提供的日志界面
```

---

## 🆘 常见问题

### Q: ESP32设备无法连接？
**A**: 检查以下项目：
1. ESP32设备IP地址是否正确
2. 网络是否互通 (`ping ESP32_IP`)
3. ESP32设备是否在线
4. 防火墙设置

### Q: 部署后无法访问？
**A**: 检查以下项目：
1. 服务是否正常启动
2. 端口是否开放
3. 域名解析是否正确
4. SSL证书是否有效

### Q: 性能问题？
**A**: 优化建议：
1. 增加服务器配置
2. 使用CDN加速
3. 启用Gzip压缩
4. 优化数据库查询

---

## 📞 获取帮助

1. **查看详细文档**: `README_DEPLOYMENT.md`
2. **提交Issue**: GitHub仓库
3. **查看日志**: 根据部署方案查看相应日志

---

## 🎉 部署成功！

部署完成后，您就可以：

- 🌐 **在任何地方访问ESP32设备管理界面**
- 📱 **使用手机、平板等设备管理**
- 👥 **与团队成员共享访问**
- 🔄 **实时同步设备状态**
- ⏰ **远程管理定时任务**

享受您的ESP32 MCP Wake公网管理体验！🚀
