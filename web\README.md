# ESP32 MCP Wake Web管理界面

## 🎯 项目简介

这是专为ESP32 MCP Wake系统设计的现代化Web管理界面，提供直观的图形界面来管理ESP32设备的远程唤醒和定时任务功能。与ESP32固件中的`mcp_wake` HTTP服务器完美配套。

## ✨ 主要特性

### 🔧 设备管理
- **实时状态监控**: 每10秒自动检查ESP32设备状态
- **远程唤醒控制**: 支持自定义唤醒提示词和强制唤醒模式
- **设备重启**: 安全的远程重启功能
- **连接状态**: 实时显示设备在线/离线状态

### ⏰ 定时任务管理
- **绝对时间任务**: 设置具体时间（如每天8:30:00）
- **相对时间任务**: 从当前时间开始计算（如5分钟后）
- **秒级精度**: 支持精确到秒的定时设置
- **重复模式**: 支持每日/每周/一次性任务
- **任务控制**: 启用/禁用/编辑/删除任务

### 🎨 现代化界面
- **响应式设计**: 支持桌面和移动设备
- **美观UI**: 基于Bootstrap 5的现代化设计
- **实时反馈**: 操作结果实时通知
- **状态可视化**: 设备状态和任务状态清晰显示

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- ESP32设备运行mcp_wake HTTP服务器（端口8081）
- 网络连接

### 2. 安装依赖
```bash
cd web
pip install -r requirements.txt
```

### 3. 启动服务器

#### 基本启动
```bash
python start.py
```

#### 指定ESP32 IP地址
```bash
python start.py --ip *************
```

#### 指定Web服务器端口
```bash
python start.py --port 8080
```

#### 启用调试模式
```bash
python start.py --debug
```

#### 完整参数示例
```bash
python start.py --ip ************* --port 8080 --debug
```

### 4. 访问Web界面
打开浏览器访问：http://localhost:5000

## 📖 使用说明

### 设备状态监控
- 界面会自动显示ESP32设备的当前状态
- 支持的状态：空闲、监听中、讲话中、连接中、激活中等
- 点击"刷新状态"按钮手动更新状态
- 可以暂停/开启自动刷新功能

### 远程唤醒控制
1. 在"唤醒消息"输入框中输入自定义提示词
2. 可选择"强制唤醒"（即使设备正在工作）
3. 点击"立即唤醒"发送指令
4. 系统会显示唤醒结果和执行状态

### 添加定时任务

#### 绝对时间任务
1. 选择"绝对时间"选项
2. 设置具体的时、分、秒
3. 选择重复日期（周日到周六）
4. 输入唤醒消息
5. 可选择"一次性任务"
6. 点击"添加定时任务"

#### 相对时间任务
1. 选择"相对时间"选项
2. 设置从现在开始的小时、分钟、秒数
3. 输入唤醒消息
4. 点击"添加定时任务"（自动为一次性任务）

### 任务管理
- **启用/禁用**: 点击任务项中的启用/禁用按钮
- **删除任务**: 点击删除按钮，确认后删除
- **查看详情**: 每个任务显示创建时间、下次执行时间、最后执行时间等信息

## 🔧 技术架构

### 后端 (Python Flask)
- **Flask Web框架**: 提供HTTP API服务
- **ESP32Client**: ESP32设备通信客户端
- **TimerTask**: 定时任务类
- **TaskManager**: 任务管理和调度
- **JSON持久化**: 任务数据保存到本地文件

### 前端 (HTML/CSS/JavaScript)
- **Bootstrap 5**: 现代化UI框架
- **原生JavaScript**: 前端应用逻辑（ES6+）
- **响应式设计**: 支持多设备访问
- **Fetch API**: 与后端API通信

### ESP32固件要求
ESP32固件必须支持以下HTTP接口：
- `POST /api/wake`: 接收唤醒指令
- `GET /api/status`: 获取设备状态
- `POST /api/reboot`: 重启设备

## 📊 API文档

### 设备控制API
- `GET /api/esp32/status`: 获取ESP32设备状态
- `POST /api/esp32/wake`: 唤醒ESP32设备
- `POST /api/esp32/reboot`: 重启ESP32设备

### 任务管理API
- `GET /api/timers`: 获取所有定时任务
- `POST /api/timers`: 添加绝对时间定时任务
- `POST /api/timers/relative`: 添加相对时间定时任务
- `PUT /api/timers/{id}`: 更新任务状态
- `DELETE /api/timers/{id}`: 删除定时任务

### 配置API
- `GET /api/config`: 获取配置信息

## 🔧 配置说明

### 环境变量
- `ESP32_IP`: ESP32设备IP地址
- `WEB_PORT`: Web服务器端口

### 启动参数
```bash
python start.py --help
```

可用参数：
- `--ip`: ESP32设备IP地址（默认：*************）
- `--port`: Web服务器端口（默认：5000）
- `--host`: Web服务器主机地址（默认：0.0.0.0）
- `--debug`: 启用调试模式

### 配置文件
任务数据保存在`timer_tasks.json`文件中，格式如下：
```json
{
  "tasks": [
    {
      "id": "task_1234567890",
      "type": "absolute",
      "time_config": {
        "time": "08:00:00",
        "repeat_days": [1, 2, 3, 4, 5]
      },
      "message": "早上好，开始新的一天！",
      "enabled": true,
      "one_time": false,
      "created_time": "2025-01-14T08:00:00",
      "next_execution": "2025-01-15T08:00:00"
    }
  ],
  "last_updated": "2025-01-14T20:30:00"
}
```

## 🐛 故障排除

### 常见问题

1. **设备连接失败**
   - 检查ESP32 IP地址是否正确
   - 确认ESP32设备已连接到网络
   - 确认ESP32运行mcp_wake HTTP服务器
   - 检查防火墙设置

2. **任务不执行**
   - 确认任务已启用
   - 检查时间设置是否正确
   - 查看服务器日志
   - 检查ESP32设备状态

3. **Web界面无法访问**
   - 确认Python服务器已启动
   - 检查端口是否被占用
   - 尝试使用 `http://127.0.0.1:5000`
   - 检查防火墙设置

### 日志调试
启用调试模式查看详细日志：
```bash
python start.py --debug
```

服务器会输出详细的日志信息，包括：
- 任务添加/删除/执行记录
- ESP32通信状态
- 错误信息和异常堆栈

## 🚀 部署说明

### 开发环境
```bash
git clone <repository>
cd web
python start.py --debug
```

### 生产环境
```bash
pip install -r requirements.txt
python start.py --ip <ESP32_IP> --port 80
```

### Docker部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "start.py", "--host", "0.0.0.0"]
```

## 🔮 路线图

- [ ] 用户认证和权限管理
- [ ] 任务执行历史记录
- [ ] 邮件/短信通知
- [ ] 任务模板功能
- [ ] 批量任务管理
- [ ] API密钥认证
- [ ] 多设备管理
- [ ] 数据库支持

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个工具！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

## 👨‍💻 作者

基于原始MCP工具开发，专为ESP32 mcp_wake系统优化

Copyright (c) 2025 PonYoung（旷）
Repository: https://github.com/onepy/Mcp_Pipe-Xiaozhi-All

---

**享受使用ESP32 MCP Wake Web管理界面！** 🎉 