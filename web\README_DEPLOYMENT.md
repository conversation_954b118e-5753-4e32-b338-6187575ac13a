# ESP32 MCP Wake Web管理界面 - 公网部署指南

## 🌐 部署方案概览

本项目支持多种公网部署方案：

1. **Docker + Nginx (推荐)** - 完整的容器化部署
2. **云平台部署** - Railway, Render, Heroku等
3. **VPS手动部署** - 传统服务器部署
4. **内网穿透** - 快速测试方案

## 🚀 方案一：Docker + Nginx 部署 (推荐)

### 前置要求
- 服务器或VPS (1GB RAM, 1 CPU核心即可)
- Docker 和 Docker Compose
- 域名 (可选，可使用IP访问)

### 快速部署

1. **克隆项目到服务器**
```bash
git clone <your-repo-url>
cd testawake/web
```

2. **配置环境变量**
```bash
cp .env.production .env
# 编辑 .env 文件，设置您的ESP32设备IP地址
nano .env
```

3. **运行部署脚本**
```bash
chmod +x deploy.sh
./deploy.sh
```

4. **访问服务**
- HTTP: `http://your-server-ip`
- HTTPS: `https://your-server-ip`

### 手动部署步骤

如果自动脚本失败，可以手动执行：

```bash
# 1. 创建目录
mkdir -p logs ssl

# 2. 生成SSL证书 (自签名)
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# 3. 构建和启动
docker-compose build
docker-compose up -d

# 4. 查看状态
docker-compose ps
docker-compose logs -f
```

## 🌩️ 方案二：云平台部署

### Railway 部署

1. **准备项目**
```bash
# 在web目录下创建railway.json
echo '{"build": {"builder": "DOCKERFILE"}, "deploy": {"startCommand": "gunicorn --bind 0.0.0.0:$PORT wsgi:app"}}' > railway.json
```

2. **部署到Railway**
- 访问 [railway.app](https://railway.app)
- 连接GitHub仓库
- 设置环境变量：`ESP32_IP`, `SECRET_KEY`
- 自动部署

### Render 部署

1. **创建render.yaml**
```yaml
services:
  - type: web
    name: esp32-mcp-wake
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn --bind 0.0.0.0:$PORT wsgi:app
    envVars:
      - key: ESP32_IP
        value: "*************"
      - key: FLASK_ENV
        value: "production"
```

2. **部署**
- 访问 [render.com](https://render.com)
- 连接GitHub仓库
- 自动部署

## 🖥️ 方案三：VPS手动部署

### Ubuntu/Debian 服务器

```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装Python和依赖
sudo apt install python3 python3-pip python3-venv nginx -y

# 3. 创建项目目录
sudo mkdir -p /var/www/esp32-wake
cd /var/www/esp32-wake

# 4. 克隆项目
git clone <your-repo-url> .
cd web

# 5. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 6. 安装依赖
pip install -r requirements.txt

# 7. 配置环境变量
cp .env.production .env
# 编辑 .env 文件

# 8. 创建systemd服务
sudo nano /etc/systemd/system/esp32-wake.service
```

**systemd服务配置** (`/etc/systemd/system/esp32-wake.service`):
```ini
[Unit]
Description=ESP32 MCP Wake Web Service
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/esp32-wake/web
Environment=PATH=/var/www/esp32-wake/web/venv/bin
ExecStart=/var/www/esp32-wake/web/venv/bin/gunicorn --bind 127.0.0.1:5000 wsgi:app
Restart=always

[Install]
WantedBy=multi-user.target
```

**Nginx配置** (`/etc/nginx/sites-available/esp32-wake`):
```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名或IP

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**启动服务**:
```bash
# 启动服务
sudo systemctl enable esp32-wake
sudo systemctl start esp32-wake

# 配置Nginx
sudo ln -s /etc/nginx/sites-available/esp32-wake /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# 配置防火墙
sudo ufw allow 80
sudo ufw allow 443
```

## 🔒 SSL证书配置

### 使用Let's Encrypt (免费)

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `ESP32_IP` | ESP32设备IP地址 | `*************` |
| `ESP32_PORT` | ESP32设备端口 | `8081` |
| `SECRET_KEY` | Flask密钥 | 随机生成 |
| `WEB_PORT` | Web服务端口 | `5000` |
| `FLASK_ENV` | 运行环境 | `production` |

### 安全建议

1. **更改默认密钥**
```bash
# 生成随机密钥
python3 -c "import secrets; print(secrets.token_hex(32))"
```

2. **配置防火墙**
```bash
# 只开放必要端口
sudo ufw enable
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
```

3. **定期更新**
```bash
# 更新系统和依赖
sudo apt update && sudo apt upgrade -y
pip install -r requirements.txt --upgrade
```

## 📊 监控和维护

### 查看日志
```bash
# Docker部署
docker-compose logs -f

# systemd部署
sudo journalctl -u esp32-wake -f

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 性能监控
```bash
# 查看资源使用
docker stats  # Docker部署
htop          # 系统资源

# 查看服务状态
curl http://localhost/api/health
```

## 🆘 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口是否被占用：`sudo netstat -tlnp | grep :5000`
   - 检查日志：`docker-compose logs` 或 `journalctl -u esp32-wake`

2. **无法访问ESP32设备**
   - 确认ESP32设备IP地址正确
   - 检查网络连通性：`ping ESP32_IP`
   - 确认ESP32设备在线

3. **SSL证书问题**
   - 检查证书文件权限
   - 重新生成证书：`openssl req -x509 -newkey rsa:4096...`

### 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 提交Issue到GitHub仓库
