#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 MCP Wake Web管理界面 - 配置文件

Copyright (c) 2025 PonYoung（旷）
Repository: https://github.com/onepy/Mcp_Pi<PERSON>-<PERSON>zhi-All
License: MIT License
"""

import os

class Config:
    """基础配置类"""
    
    # ESP32设备配置
    ESP32_IP = os.environ.get('ESP32_IP', '************')
    ESP32_PORT = int(os.environ.get('ESP32_PORT', '8081'))
    ESP32_TIMEOUT = int(os.environ.get('ESP32_TIMEOUT', '5'))
    
    # Web服务器配置
    WEB_HOST = os.environ.get('WEB_HOST', '0.0.0.0')
    WEB_PORT = int(os.environ.get('WEB_PORT', '5000'))
    
    # 任务管理配置
    TASKS_FILE = os.environ.get('TASKS_FILE', 'timer_tasks.json')
    CHECK_INTERVAL = int(os.environ.get('CHECK_INTERVAL', '1'))
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'esp32-mcp-wake-secret-key')
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'web_server.log')
    
    @classmethod
    def validate_config(cls):
        """验证配置参数"""
        errors = []
        
        # 验证IP地址格式
        import re
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(ip_pattern, cls.ESP32_IP):
            errors.append(f"无效的ESP32 IP地址: {cls.ESP32_IP}")
        
        # 验证端口范围
        if not (1 <= cls.ESP32_PORT <= 65535):
            errors.append(f"无效的ESP32端口: {cls.ESP32_PORT}")
        
        if not (1 <= cls.WEB_PORT <= 65535):
            errors.append(f"无效的Web端口: {cls.WEB_PORT}")
        
        # 验证超时时间
        if cls.ESP32_TIMEOUT <= 0:
            errors.append(f"无效的超时时间: {cls.ESP32_TIMEOUT}")
        
        # 验证检查间隔
        if cls.CHECK_INTERVAL <= 0:
            errors.append(f"无效的检查间隔: {cls.CHECK_INTERVAL}")
        
        if errors:
            raise ValueError('\n'.join(errors))
        
        return True
    
    @classmethod
    def get_esp32_url(cls):
        """获取ESP32设备的完整URL"""
        return f"http://{cls.ESP32_IP}:{cls.ESP32_PORT}"
    
    @classmethod
    def to_dict(cls):
        """转换为字典格式"""
        return {
            'esp32_ip': cls.ESP32_IP,
            'esp32_port': cls.ESP32_PORT,
            'esp32_timeout': cls.ESP32_TIMEOUT,
            'web_host': cls.WEB_HOST,
            'web_port': cls.WEB_PORT,
            'tasks_file': cls.TASKS_FILE,
            'check_interval': cls.CHECK_INTERVAL,
            'debug': cls.DEBUG
        }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': Config
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config_map.get(env, Config)