version: '3.8'

services:
  esp32-web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - ESP32_IP=${ESP32_IP:-*************}
      - ESP32_PORT=${ESP32_PORT:-8081}
      - WEB_HOST=0.0.0.0
      - WEB_PORT=5000
      - SECRET_KEY=${SECRET_KEY:-esp32-mcp-wake-production-secret-key-2025}
    volumes:
      - ./devices.json:/app/devices.json
      - ./timer_tasks.json:/app/timer_tasks.json
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl  # SSL证书目录
    depends_on:
      - esp32-web
    restart: unless-stopped
