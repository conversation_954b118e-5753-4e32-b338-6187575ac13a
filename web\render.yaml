services:
  - type: web
    name: esp32-mcp-wake
    env: python
    region: singapore  # 选择离您最近的区域
    plan: free  # 或 starter
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 wsgi:app
    healthCheckPath: /api/health
    envVars:
      - key: FLASK_ENV
        value: production
      - key: ESP32_IP
        value: "*************"  # 替换为您的ESP32 IP
      - key: ESP32_PORT
        value: "8081"
      - key: SECRET_KEY
        generateValue: true  # 自动生成随机密钥
      - key: WEB_HOST
        value: "0.0.0.0"
      - key: LOG_LEVEL
        value: "INFO"
