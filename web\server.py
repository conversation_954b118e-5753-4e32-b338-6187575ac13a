#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 MCP Wake Web 管理界面
与 ESP32 mcp_wake HTTP服务器配套的Web管理工具

Copyright (c) 2025 PonYoung（旷）
Repository: https://github.com/onepy/Mcp_Pipe-Xiaozhi-All
License: MIT License
"""

import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from flask import Flask, render_template, request, jsonify, render_template_string, make_response
import requests
from dataclasses import dataclass

# ==================== 配置参数 ====================
DEFAULT_ESP32_IP = "************"   # 默认ESP32设备IP地址
DEFAULT_ESP32_PORT = 8081            # mcp_wake HTTP服务器端口
WEB_SERVER_PORT = 5000               # Web服务器端口
TASKS_FILE = "timer_tasks.json"      # 任务数据文件
DEVICES_FILE = "devices.json"        # 设备列表文件
CHECK_INTERVAL = 1                   # 任务检查间隔（秒）

app = Flask(__name__)

class ESP32Client:
    """ESP32设备HTTP客户端，与mcp_wake服务器通信"""
    
    def __init__(self, ip: str = DEFAULT_ESP32_IP, port: int = DEFAULT_ESP32_PORT):
        self.ip = ip
        self.port = port
        self.base_url = f"http://{ip}:{port}"
        self.timeout = 5
        
    def send_message(self, message: str, force: bool = False) -> Dict[str, Any]:
        """
        发送消息到ESP32设备
        
        Args:
            message: 消息内容
            force: 是否强制发送
            
        Returns:
            包含执行结果的字典
        """
        try:
            payload = {
                "message": message,
                "force": force
            }
            
            response = requests.post(
                f"{self.base_url}/api/wake",
                json=payload,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "success": False,
                    "message": f"HTTP错误: {response.status_code}",
                    "status_code": response.status_code
                }
                
        except requests.exceptions.Timeout:
            return {"success": False, "message": "请求超时，设备可能未连接"}
        except requests.exceptions.ConnectionError:
            return {"success": False, "message": "连接失败，请检查设备IP和网络"}
        except Exception as e:
            return {"success": False, "message": f"未知错误: {str(e)}"}

    # 保持向后兼容
    def wake_device(self, message: str, force: bool = False) -> Dict[str, Any]:
        """向后兼容的方法名"""
        return self.send_message(message, force)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取ESP32设备状态
        
        Returns:
            包含设备状态的字典
        """
        try:
            response = requests.get(
                f"{self.base_url}/api/status",
                timeout=self.timeout
            )
            
            # 使用raise_for_status()来处理HTTP错误
            response.raise_for_status()
            
            # 直接返回ESP32的原始JSON响应，添加连接信息
            data = response.json()
            data["connected"] = True
            data["ip_address"] = self.ip
            return data
                
        except requests.exceptions.Timeout:
            return {
                "connected": False,
                "message": "请求超时",
                "device_state": "timeout",
                "success": False
            }
        except requests.exceptions.ConnectionError:
            return {
                "connected": False,
                "message": "设备离线",
                "device_state": "offline",
                "success": False
            }
        except requests.exceptions.HTTPError as e:
            return {
                "connected": False,
                "message": f"HTTP错误: {e.response.status_code}",
                "device_state": "error",
                "success": False
            }
        except requests.exceptions.RequestException as e:
            return {
                "connected": False,
                "message": f"网络请求失败: {str(e)}",
                "device_state": "error", 
                "success": False
            }
        except json.JSONDecodeError as e:
            return {
                "connected": False,
                "message": f"响应解析失败: {str(e)}",
                "device_state": "error",
                "success": False
            }
        except Exception as e:
            return {
                "connected": False,
                "message": f"未知错误: {str(e)}",
                "device_state": "error",
                "success": False
            }
        
    def reboot_device(self) -> Dict[str, Any]:
        """
        重启ESP32设备
        
        Returns:
            包含执行结果的字典
        """
        try:
            payload = {"confirm": True}
            
            response = requests.post(
                f"{self.base_url}/api/reboot",
                json=payload,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "success": False,
                    "message": f"HTTP错误: {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "message": f"重启失败: {str(e)}"}

class TimerTask:
    """定时任务类"""
    
    def __init__(self, task_id: str, task_type: str, time_config: Dict, 
                 message: str, enabled: bool = True, one_time: bool = False):
        self.id = task_id
        self.type = task_type  # 'absolute' 或 'relative'
        self.time_config = time_config
        self.message = message
        self.enabled = enabled
        self.one_time = one_time
        self.created_time = datetime.now().isoformat()
        self.last_executed = None
        self.next_execution = None
        
        self._calculate_next_execution()
    
    def _calculate_next_execution(self):
        """计算下次执行时间"""
        if not self.enabled:
            self.next_execution = None
            return
            
        now = datetime.now()
        
        if self.type == 'relative':
            # 相对时间任务：从当前时间计算
            hours = self.time_config.get('hours', 0)
            minutes = self.time_config.get('minutes', 0)
            seconds = self.time_config.get('seconds', 0)
            
            delta = timedelta(hours=hours, minutes=minutes, seconds=seconds)
            self.next_execution = (now + delta).isoformat()
            
        elif self.type == 'absolute':
            # 绝对时间任务：根据时间和重复日期计算
            target_time = self.time_config['time']  # "HH:MM:SS"
            repeat_days = self.time_config.get('repeat_days', [])
            
            hour, minute, second = map(int, target_time.split(':'))
            
            # 找到下一个符合条件的日期
            for i in range(8):  # 最多检查未来7天
                candidate = now + timedelta(days=i)
                candidate_weekday = candidate.weekday()
                # 转换为周日=0的格式
                candidate_weekday = (candidate_weekday + 1) % 7
                
                candidate_time = candidate.replace(
                    hour=hour, minute=minute, second=second, microsecond=0
                )
                
                # 如果是今天，检查时间是否已过
                if i == 0 and candidate_time <= now:
                    continue
                    
                # 检查是否在重复日期中
                if candidate_weekday in repeat_days:
                    self.next_execution = candidate_time.isoformat()
                    break
    
    def should_execute(self) -> bool:
        """检查是否应该执行"""
        if not self.enabled or not self.next_execution:
            return False
            
        now = datetime.now()
        next_time = datetime.fromisoformat(self.next_execution)
        
        return now >= next_time
    
    def mark_executed(self):
        """标记任务已执行"""
        self.last_executed = datetime.now().isoformat()
        
        if self.one_time:
            # 一次性任务执行后禁用
            self.enabled = False
            self.next_execution = None
        else:
            # 重复任务重新计算下次执行时间
            self._calculate_next_execution()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'type': self.type,
            'time_config': self.time_config,
            'message': self.message,
            'enabled': self.enabled,
            'one_time': self.one_time,
            'created_time': self.created_time,
            'last_executed': self.last_executed,
            'next_execution': self.next_execution
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TimerTask':
        """从字典创建任务"""
        task = cls(
            task_id=data['id'],
            task_type=data['type'],
            time_config=data['time_config'],
            message=data['message'],
            enabled=data.get('enabled', True),
            one_time=data.get('one_time', False)
        )
        task.created_time = data.get('created_time', task.created_time)
        task.last_executed = data.get('last_executed')
        task.next_execution = data.get('next_execution')
        return task

class TaskManager:
    """定时任务管理器"""
    
    def __init__(self, esp32_client: ESP32Client, tasks_file: str = TASKS_FILE):
        self.esp32_client = esp32_client
        self.tasks_file = tasks_file
        self.tasks = {}
        self.running = False
        self.check_thread = None
        
        self.load_tasks()
        self.start_checking()  # 🔧 自动启动任务检查器

    def load_tasks(self):
        """从文件加载任务"""
        try:
            if os.path.exists(self.tasks_file):
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for task_data in data.get('tasks', []):
                        task = TimerTask.from_dict(task_data)
                        self.tasks[task.id] = task
                print(f"📂 加载了 {len(self.tasks)} 个定时任务")
        except Exception as e:
            print(f"❌ 加载任务失败: {e}")

    def save_tasks(self):
        """保存任务到文件"""
        try:
            data = {
                'tasks': [task.to_dict() for task in self.tasks.values()],
                'last_updated': datetime.now().isoformat()
            }
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 保存了 {len(self.tasks)} 个定时任务")
        except Exception as e:
            print(f"❌ 保存任务失败: {e}")

    def add_task(self, task_type: str, time_config: Dict, message: str, 
                 one_time: bool = False) -> str:
        """添加定时任务"""
        task_id = f"task_{int(time.time() * 1000)}"
        task = TimerTask(task_id, task_type, time_config, message, True, one_time)
        self.tasks[task_id] = task
        self.save_tasks()
        
        print(f"➕ 添加定时任务: {task_id} - {message}")
        print(f"   📅 下次执行时间: {task.next_execution}")
        
        return task_id

    def delete_task(self, task_id: str) -> bool:
        """删除定时任务"""
        if task_id in self.tasks:
            task = self.tasks.pop(task_id)
            self.save_tasks()
            print(f"🗑️ 删除定时任务: {task_id} - {task.message}")
            return True
        return False

    def update_task(self, task_id: str, enabled: bool) -> bool:
        """更新定时任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.enabled = enabled
            task._calculate_next_execution()  # 重新计算下次执行时间
            self.save_tasks()
            print(f"🔄 更新任务状态: {task_id} -> {'启用' if enabled else '禁用'}")
            return True
        return False

    def get_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务列表"""
        return [task.to_dict() for task in self.tasks.values()]

    def start_checking(self):
        """启动任务检查线程"""
        if self.running:
            return
            
        self.running = True
        self.check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self.check_thread.start()
        print("🔄 定时任务检查器已启动")
    
    def stop_checking(self):
        """停止任务检查"""
        self.running = False
        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=1.0)  # 添加超时避免无限等待
        self.check_thread = None
        print("⏹️ 定时任务检查器已停止")
    
    def _check_loop(self):
        """任务检查循环"""
        while self.running:
            try:
                current_time = datetime.now()
                executed_tasks = []
                
                for task in list(self.tasks.values()):
                    if task.should_execute():
                        print(f"⏰ 执行定时任务: {task.id} - {task.message}")
                        print(f"   🕐 预定时间: {task.next_execution}")
                        print(f"   🕐 当前时间: {current_time.isoformat()}")
                        
                        # 执行唤醒操作
                        result = self.esp32_client.wake_device(task.message, force=False)
                        
                        if result.get('success'):
                            print(f"✅ 任务执行成功: {task.message}")
                        else:
                            print(f"❌ 任务执行失败: {result.get('message', '未知错误')}")
                        
                        # 标记任务已执行
                        task.mark_executed()
                        executed_tasks.append(task.id)
                
                # 如果有任务被执行，保存状态
                if executed_tasks:
                    self.save_tasks()
                    print(f"📝 已更新 {len(executed_tasks)} 个任务的执行状态")
                
                time.sleep(CHECK_INTERVAL)
                
            except Exception as e:
                print(f"❌ 任务检查出错: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(CHECK_INTERVAL)

class DeviceManager:
    """设备管理器，管理多个ESP32设备"""
    
    def __init__(self, devices_file: str = DEVICES_FILE):
        self.devices_file = devices_file
        self.devices = {}  # device_id -> ESP32Client
        self.device_info = {}  # device_id -> device_info
        self.load_devices()
    
    def load_devices(self):
        """从文件加载设备列表"""
        try:
            if os.path.exists(self.devices_file):
                with open(self.devices_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for device_id, info in data.items():
                        self.device_info[device_id] = info
                        self.devices[device_id] = ESP32Client(info['ip'], info.get('port', DEFAULT_ESP32_PORT))
            else:
                # 创建默认设备
                self.add_device("default", "默认设备", DEFAULT_ESP32_IP, DEFAULT_ESP32_PORT)
        except Exception as e:
            print(f"加载设备列表失败: {e}")
            # 创建默认设备
            self.add_device("default", "默认设备", DEFAULT_ESP32_IP, DEFAULT_ESP32_PORT)
    
    def save_devices(self):
        """保存设备列表到文件"""
        try:
            with open(self.devices_file, 'w', encoding='utf-8') as f:
                json.dump(self.device_info, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设备列表失败: {e}")
    
    def add_device(self, device_id: str, name: str, ip: str, port: int = DEFAULT_ESP32_PORT) -> bool:
        """添加新设备"""
        try:
            self.device_info[device_id] = {
                "name": name,
                "ip": ip,
                "port": port,
                "created_time": datetime.now().isoformat()
            }
            self.devices[device_id] = ESP32Client(ip, port)
            self.save_devices()
            return True
        except Exception as e:
            print(f"添加设备失败: {e}")
            return False
    
    def remove_device(self, device_id: str) -> bool:
        """删除设备"""
        try:
            if device_id in self.devices:
                del self.devices[device_id]
            if device_id in self.device_info:
                del self.device_info[device_id]
            self.save_devices()
            return True
        except Exception as e:
            print(f"删除设备失败: {e}")
            return False
    
    def update_device(self, device_id: str, name: str = None, ip: str = None, port: int = None) -> bool:
        """更新设备信息"""
        try:
            if device_id not in self.device_info:
                return False
            
            # 更新设备信息
            if name is not None:
                self.device_info[device_id]['name'] = name
            if ip is not None:
                self.device_info[device_id]['ip'] = ip
            if port is not None:
                self.device_info[device_id]['port'] = port
            
            # 如果IP或端口发生变化，重新创建客户端
            if ip is not None or port is not None:
                current_ip = self.device_info[device_id]['ip']
                current_port = self.device_info[device_id]['port']
                self.devices[device_id] = ESP32Client(current_ip, current_port)
            
            # 更新修改时间
            self.device_info[device_id]['updated_time'] = datetime.now().isoformat()
            
            self.save_devices()
            return True
        except Exception as e:
            print(f"更新设备失败: {e}")
            return False
    
    def get_device(self, device_id: str) -> Optional[ESP32Client]:
        """获取设备客户端"""
        return self.devices.get(device_id)
    
    def get_device_info(self, device_id: str) -> Optional[Dict]:
        """获取设备信息"""
        return self.device_info.get(device_id)
    
    def get_all_devices(self) -> Dict[str, Dict]:
        """获取所有设备信息"""
        result = {}
        for device_id, info in self.device_info.items():
            client = self.devices.get(device_id)
            if client:
                status = client.get_status()
                result[device_id] = {
                    **info,
                    "device_id": device_id,
                    "status": status,
                    "online": status.get("connected", False)
                }
        return result
    
    def send_message_to_device(self, device_id: str, message: str, force: bool = False) -> Dict[str, Any]:
        """向指定设备发送消息"""
        client = self.get_device(device_id)
        if not client:
            return {"success": False, "message": f"设备 {device_id} 不存在"}
        
        return client.send_message(message, force)
    
    def send_message_between_devices(self, from_device_id: str, to_device_id: str, message: str, force: bool = False) -> Dict[str, Any]:
        """设备间消息发送"""
        from_info = self.get_device_info(from_device_id)
        to_info = self.get_device_info(to_device_id)
        
        if not from_info:
            return {"success": False, "message": f"源设备 {from_device_id} 不存在"}
        if not to_info:
            return {"success": False, "message": f"目标设备 {to_device_id} 不存在"}
        
        # 构造设备间消息格式
        inter_device_message = f"来自设备【{from_info['name']}】的消息: {message}"
        
        return self.send_message_to_device(to_device_id, inter_device_message, force)

# 全局变量
esp32_client = ESP32Client()
task_manager = TaskManager(esp32_client)
device_manager = DeviceManager()

# ==================== Web API 路由 ====================

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查基本服务状态
        status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'services': {
                'task_manager': task_manager is not None,
                'device_manager': device_manager is not None,
                'esp32_client': esp32_client is not None
            }
        }
        return jsonify(status), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    return jsonify({
        'esp32_ip': esp32_client.ip,
        'esp32_port': esp32_client.port,
        'web_port': WEB_SERVER_PORT
    })

@app.route('/api/esp32/status')
def esp32_status():
    """获取ESP32设备状态"""
    status = esp32_client.get_status()
    return jsonify(status)

@app.route('/api/esp32/wake', methods=['POST'])
def esp32_wake():
    """唤醒ESP32设备"""
    try:
        data = request.get_json()
        message = data.get('message', '远程唤醒')
        force = data.get('force', True)
        
        result = esp32_client.wake_device(message, force)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'请求处理失败: {str(e)}'
        }), 400

@app.route('/api/esp32/reboot', methods=['POST'])
def esp32_reboot():
    """重启ESP32设备"""
    try:
        result = esp32_client.reboot_device()
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重启失败: {str(e)}'
        }), 400

@app.route('/api/timers')
def get_timers():
    """获取所有定时任务"""
    tasks = task_manager.get_tasks()
    return jsonify({
        'success': True,
        'tasks': tasks,
        'count': len(tasks)
    })

@app.route('/api/timers', methods=['POST'])
def add_timer():
    """添加绝对时间定时任务"""
    try:
        data = request.get_json()
        
        time_config = {
            'time': f"{data['hour']:02d}:{data['minute']:02d}:{data['second']:02d}",
            'repeat_days': data.get('repeat_days', [])
        }
        
        task_id = task_manager.add_task(
            task_type='absolute',
            time_config=time_config,
            message=data['message'],
            one_time=data.get('one_time', False)
        )
        
        return jsonify({
            'success': True,
            'message': '定时任务添加成功',
            'task_id': task_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加任务失败: {str(e)}'
        }), 400

@app.route('/api/timers/relative', methods=['POST'])
def add_relative_timer():
    """添加相对时间定时任务"""
    try:
        data = request.get_json()
        
        time_config = {
            'hours': data.get('hours', 0),
            'minutes': data.get('minutes', 0),
            'seconds': data.get('seconds', 0)
        }
        
        task_id = task_manager.add_task(
            task_type='relative',
            time_config=time_config,
            message=data['message'],
            one_time=True  # 相对时间任务通常是一次性的
        )
        
        return jsonify({
            'success': True,
            'message': '相对时间任务添加成功',
            'task_id': task_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加任务失败: {str(e)}'
        }), 400

@app.route('/api/timers/<task_id>', methods=['PUT'])
def update_timer(task_id):
    """更新定时任务"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', True)
        
        if task_manager.update_task(task_id, enabled):
            return jsonify({
                'success': True,
                'message': '任务更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新任务失败: {str(e)}'
        }), 400

@app.route('/api/timers/<task_id>', methods=['DELETE'])
def delete_timer(task_id):
    """删除定时任务"""
    try:
        if task_manager.delete_task(task_id):
            return jsonify({
                'success': True,
                'message': '任务删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除任务失败: {str(e)}'
        }), 400

# ==================== 设备管理 API ====================

@app.route('/api/devices')
def get_devices():
    """获取所有设备列表"""
    try:
        devices = device_manager.get_all_devices()
        
        # 检查是否是ESP32设备请求（通过User-Agent或特殊参数识别）
        is_esp32_request = (
            'ESP32' in request.headers.get('User-Agent', '') or
            request.args.get('format') == 'esp32' or
            'esp32' in request.headers.get('User-Agent', '').lower()
        )
        
        if is_esp32_request:
            # 为ESP32提供简化的设备配置格式
            esp32_config = {}
            for device_id, device_info in devices.items():
                esp32_config[device_id] = {
                    'name': device_info['name'],
                    'ip': device_info['ip'],
                    'port': device_info['port']
                }
            # 确保中文字符不被转义编码，使用紧凑格式（无缩进）
            response = make_response(json.dumps(esp32_config, ensure_ascii=False, separators=(',', ':')))
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            return response
        else:
            # 为Web界面提供完整的设备信息
            return jsonify({
                'success': True,
                'devices': devices,
                'count': len(devices)
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取设备列表失败: {str(e)}'
        }), 500

@app.route('/api/devices', methods=['POST'])
def add_device():
    """添加新设备"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')
        name = data.get('name')
        ip = data.get('ip')
        port = data.get('port', DEFAULT_ESP32_PORT)
        
        if not device_id or not name or not ip:
            return jsonify({
                'success': False,
                'message': '设备ID、名称和IP地址不能为空'
            }), 400
        
        # 检查设备ID是否已存在
        if device_manager.get_device_info(device_id):
            return jsonify({
                'success': False,
                'message': f'设备ID {device_id} 已存在'
            }), 400
        
        success = device_manager.add_device(device_id, name, ip, port)
        
        if success:
            return jsonify({
                'success': True,
                'message': '设备添加成功',
                'device_id': device_id
            })
        else:
            return jsonify({
                'success': False,
                'message': '设备添加失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'添加设备失败: {str(e)}'
        }), 500

@app.route('/api/devices/<device_id>', methods=['PUT'])
def update_device(device_id):
    """更新设备信息"""
    try:
        data = request.get_json()
        name = data.get('name')
        ip = data.get('ip')
        port = data.get('port')
        
        # 检查设备是否存在
        if not device_manager.get_device_info(device_id):
            return jsonify({
                'success': False,
                'message': '设备不存在'
            }), 404
        
        # 至少需要提供一个要更新的字段
        if name is None and ip is None and port is None:
            return jsonify({
                'success': False,
                'message': '请提供要更新的字段'
            }), 400
        
        # 验证输入
        if name is not None and not name.strip():
            return jsonify({
                'success': False,
                'message': '设备名称不能为空'
            }), 400
        
        success = device_manager.update_device(device_id, name, ip, port)
        
        if success:
            return jsonify({
                'success': True,
                'message': '设备更新成功',
                'device_id': device_id
            })
        else:
            return jsonify({
                'success': False,
                'message': '设备更新失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新设备失败: {str(e)}'
        }), 500

@app.route('/api/devices/<device_id>', methods=['DELETE'])
def remove_device(device_id):
    """删除设备"""
    try:
        # 不允许删除默认设备
        if device_id == 'default':
            return jsonify({
                'success': False,
                'message': '不能删除默认设备'
            }), 400
        
        success = device_manager.remove_device(device_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '设备删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '设备不存在'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除设备失败: {str(e)}'
        }), 500

@app.route('/api/devices/<device_id>/status')
def get_device_status(device_id):
    """获取指定设备状态"""
    try:
        client = device_manager.get_device(device_id)
        if not client:
            return jsonify({
                'success': False,
                'message': f'设备 {device_id} 不存在'
            }), 404
        
        status = client.get_status()
        return jsonify(status)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取设备状态失败: {str(e)}'
        }), 500

@app.route('/api/devices/<device_id>/send', methods=['POST'])
def send_message_to_device_api(device_id):
    """向指定设备发送消息"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        force = data.get('force', True)
        
        if not message:
            return jsonify({
                'success': False,
                'message': '消息内容不能为空'
            }), 400
        
        result = device_manager.send_message_to_device(device_id, message, force)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发送消息失败: {str(e)}'
        }), 500

@app.route('/api/devices/send-between', methods=['POST'])
def send_message_between_devices_api():
    """设备间消息发送（通过中转服务器）"""
    try:
        data = request.get_json()
        from_device_id = data.get('from_device_id')
        to_device_id = data.get('to_device_id')
        message = data.get('message', '')
        force = data.get('force', False)
        
        if not from_device_id or not to_device_id or not message:
            return jsonify({
                'success': False,
                'message': '源设备、目标设备和消息内容不能为空'
            }), 400
        
        if from_device_id == to_device_id:
            return jsonify({
                'success': False,
                'message': '源设备和目标设备不能相同'
            }), 400
        
        result = device_manager.send_message_between_devices(from_device_id, to_device_id, message, force)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'设备间消息发送失败: {str(e)}'
        }), 500

@app.route('/api/devices/send-direct', methods=['POST'])
def send_message_direct():
    """设备间直接通信（不通过中转服务器）"""
    try:
        data = request.get_json()
        to_device_id = data.get('to_device_id')
        message = data.get('message', '')
        force = data.get('force', False)
        
        if not to_device_id or not message:
            return jsonify({
                'success': False,
                'message': '目标设备和消息内容不能为空'
            }), 400
        
        # 获取目标设备信息
        target_device = device_manager.get_device_info(to_device_id)
        if not target_device:
            return jsonify({
                'success': False,
                'message': f'目标设备 {to_device_id} 不存在'
            }), 404
        
        # 返回目标设备的连接信息，让前端直接连接
        return jsonify({
            'success': True,
            'message': '获取设备连接信息成功',
            'target_device': {
                'device_id': to_device_id,
                'name': target_device['name'],
                'ip': target_device['ip'],
                'port': target_device['port'],
                'url': f"http://{target_device['ip']}:{target_device['port']}"
            },
            'payload': {
                'message': message,
                'force': force
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取设备信息失败: {str(e)}'
        }), 500

@app.route('/api/devices/voice-command', methods=['POST'])
def process_voice_command():
    """处理来自ESP32设备的语音命令设备间通信"""
    try:
        data = request.get_json()
        target_device = data.get('target_device', '')
        message = data.get('message', '')
        source_device_id = data.get('source_device_id', 'default')
        force = data.get('force', False)
        
        if not target_device or not message:
            return jsonify({
                'success': False,
                'message': '目标设备和消息内容不能为空'
            }), 400
        
        # 查找目标设备
        devices = device_manager.get_all_devices()
        target_device_id = None
        
        # 支持多种匹配方式：设备ID、设备名称、模糊匹配
        for device_id, device_info in devices.items():
            if (device_info['name'] == target_device or
                device_id == target_device or
                target_device.lower() in device_info['name'].lower() or
                target_device in device_id):
                target_device_id = device_id
                break
        
        if not target_device_id:
            return jsonify({
                'success': False,
                'message': f'未找到目标设备: {target_device}',
                'available_devices': [info['name'] for info in devices.values()]
            }), 404
        
        # 直接向目标设备发送消息
        result = device_manager.send_message_to_device(target_device_id, message, force)
        
        if result.get('success'):
            return jsonify({
                'success': True,
                'message': f'语音命令执行成功，已向{devices[target_device_id]["name"]}发送消息',
                'target_device': devices[target_device_id]['name'],
                'target_device_id': target_device_id,
                'sent_message': message,
                'source_device': source_device_id,
                'communication_result': result
            })
        else:
            return jsonify({
                'success': False,
                'message': f'向目标设备发送消息失败: {result.get("message", "未知错误")}',
                'target_device': target_device,
                'communication_result': result
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'语音命令处理失败: {str(e)}'
        }), 500

def main():
    """主函数"""
    print("🚀 ESP32 MCP Wake Web管理界面")
    print("=" * 50)
    print(f"📡 ESP32设备地址: {esp32_client.ip}:{esp32_client.port}")
    print(f"🌐 Web服务器端口: {WEB_SERVER_PORT}")
    print("=" * 50)
    
    # 启动任务管理器
    task_manager.start_checking()
    
    try:
        # 启动Web服务器
        app.run(
            host='0.0.0.0',
            port=WEB_SERVER_PORT,
            debug=False,  # 生产环境关闭debug
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 正在停止服务器...")
    finally:
        task_manager.stop_checking()
        print("✅ 服务器已停止")

if __name__ == '__main__':
    main()