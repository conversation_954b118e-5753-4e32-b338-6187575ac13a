#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 MCP Wake Web管理界面 - 启动脚本
优化版本，与mcp_wake完美配套

Copyright (c) 2025 PonYoung（旷）
Repository: https://github.com/onepy/Mcp_Pipe-Xiaozhi-All
License: MIT License
"""

import os
import sys
import subprocess
import importlib.util
import argparse

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: Python {sys.version}")
        return False
    else:
        print(f"✅ Python版本检查通过: {sys.version}")
        return True

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = ['flask', 'requests']
    missing_packages = []
    
    for package in required_packages:
        spec = importlib.util.find_spec(package)
        if spec is None:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("正在自动安装依赖...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 自动安装失败，请手动运行: pip install -r requirements.txt")
            return False
    else:
        print("✅ 依赖包检查通过")
        return True

def create_directories():
    """创建必要的目录"""
    directories = ['templates', 'static/css', 'static/js']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"📁 创建目录: {directory}")
    
    print("✅ 目录结构检查完成")

def show_info(esp32_ip, web_port, debug_mode):
    """显示启动信息"""
    print("\n" + "="*60)
    print("🚀 ESP32 MCP Wake Web管理界面")
    print("="*60)
    print(f"📡 ESP32设备地址: {esp32_ip}:8081")
    print(f"🌐 Web服务器端口: {web_port}")
    print(f"📍 访问地址: http://localhost:{web_port}")
    print(f"📍 本地地址: http://127.0.0.1:{web_port}")
    print("="*60)
    print("📋 功能特性:")
    print("  • ESP32设备状态实时监控")
    print("  • 自定义唤醒提示词")
    print("  • 绝对时间定时任务")
    print("  • 相对时间定时任务")
    print("  • 任务管理 (增删改查)")
    print("  • 强制唤醒模式")
    print("  • 设备远程重启")
    print("="*60)
    print("⚠️  注意事项:")
    print("  • 确保ESP32设备已连接到网络")
    print("  • 确保ESP32运行mcp_wake HTTP服务器")
    print("  • 使用 --ip 参数修改ESP32设备IP地址")
    print("  • 按Ctrl+C停止服务器")
    if debug_mode:
        print("  • 调试模式已启用")
    print("="*60)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='ESP32 MCP Wake Web管理界面',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python start.py                           # 使用默认设置启动
  python start.py --ip *************       # 指定ESP32 IP地址
  python start.py --port 8080              # 指定Web服务器端口
  python start.py --debug                  # 启用调试模式
        """
    )
    
    parser.add_argument(
        '--ip', 
        default='*************',
        help='ESP32设备IP地址 (默认: ************)'
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=5000,
        help='Web服务器端口 (默认: 5000)'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--host', 
        default='0.0.0.0',
        help='Web服务器主机地址 (默认: 0.0.0.0)'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    print("🔧 正在检查运行环境...")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 显示信息
    show_info(args.ip, args.port, args.debug)
    
    # 设置环境变量
    os.environ['ESP32_IP'] = args.ip
    os.environ['WEB_PORT'] = str(args.port)
    
    # 启动Web服务器
    try:
        print("\n🚀 启动Web服务器...")
        print("按Ctrl+C停止服务器\n")
        
        # 动态修改server.py中的配置
        import server
        server.esp32_client.ip = args.ip
        server.esp32_client.base_url = f"http://{args.ip}:8081"

        # 同步更新设备管理器中的默认设备IP地址
        print(f"🔄 同步默认设备IP地址: {args.ip}")
        if server.device_manager.update_device("default", ip=args.ip):
            print("✅ 默认设备IP地址已更新")
        else:
            # 如果默认设备不存在，创建它
            print("📱 创建默认设备...")
            server.device_manager.add_device("default", "默认设备", args.ip, 8081)
        
        # 启动Flask应用
        server.app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("确保server.py文件存在且无语法错误")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print(f"请检查端口{args.port}是否被占用")

if __name__ == "__main__":
    main() 