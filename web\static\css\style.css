/* ESP32 MCP Wake Web管理界面样式 */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,.1);
    --transition: all 0.3s ease;
}

body {
    background-color: var(--light-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    box-shadow: var(--box-shadow);
    border-bottom: 3px solid rgba(255,255,255,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,.15);
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 设备状态卡片 */
.device-status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.device-status-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(255,255,255,.2);
}

.device-status-card .card-body {
    background: rgba(255,255,255,.05);
}

/* 状态指示器 */
.status-item {
    margin-bottom: 10px;
}

.status-item label {
    font-weight: 600;
    margin-right: 8px;
    opacity: 0.9;
    font-size: 0.9rem;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-idle { 
    background-color: var(--success-color); 
    color: white;
}

.status-listening { 
    background-color: var(--info-color); 
    color: white;
}

.status-speaking { 
    background-color: var(--warning-color); 
    color: #212529;
}

.status-error { 
    background-color: var(--danger-color); 
    color: white;
}

.status-offline { 
    background-color: var(--dark-color); 
    color: white;
}

/* 任务项样式 */
.task-item {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    background: white;
    transition: var(--transition);
    position: relative;
}

.task-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 15px rgba(0,123,255,.15);
    transform: translateY(-1px);
}

.task-item.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.task-item.disabled:hover {
    transform: none;
    box-shadow: var(--box-shadow);
}

/* 任务类型指示器 */
.task-type-absolute {
    border-left: 4px solid var(--primary-color);
}

.task-type-relative {
    border-left: 4px solid var(--success-color);
}

/* 任务元信息 */
.task-meta {
    font-size: 0.85em;
    color: #6c757d;
    line-height: 1.4;
}

.task-meta div {
    margin-bottom: 4px;
}

.task-meta i {
    width: 14px;
    text-align: center;
}

/* 任务控制按钮 */
.task-controls {
    display: flex;
    gap: 8px;
    align-items: flex-start;
}

.task-controls .btn {
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 周选择器 */
.week-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.form-check-inline {
    margin-right: 0;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 定时器类型面板 */
.timer-type-panel {
    transition: var(--transition);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.3;
    color: var(--primary-color);
}

.empty-state p {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* 加载状态 */
.loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 通知样式 */
#notification-area {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 350px;
    max-width: 500px;
}

.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 6px 10px;
    border-radius: 12px;
    font-weight: 600;
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: var(--dark-color);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,.3);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,.1);
    background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .task-item {
        padding: 15px;
    }
    
    .task-controls {
        flex-direction: column;
        gap: 5px;
    }
    
    .week-selector {
        justify-content: center;
    }
    
    .status-item {
        text-align: center;
        margin-bottom: 15px;
    }
    
    #notification-area {
        left: 15px;
        right: 15px;
        min-width: auto;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .empty-state {
        padding: 40px 15px;
    }
    
    .empty-state i {
        font-size: 3rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card, .task-item, .alert {
    animation: fadeIn 0.3s ease-out;
}

/* 焦点样式 */
.btn:focus, .form-control:focus, .form-check-input:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 禁用状态 */
.btn:disabled, .form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 工具提示增强 */
[title] {
    cursor: help;
}

/* 设备管理样式 */
.device-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
}

.device-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.device-item.device-online {
    border-left: 4px solid var(--success-color);
}

.device-item.device-offline {
    border-left: 4px solid var(--danger-color);
    opacity: 0.7;
}

.device-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.device-meta > div {
    margin-bottom: 0.25rem;
}

.device-controls {
    display: flex;
    gap: 0.5rem;
}

.device-controls .btn {
    padding: 0.25rem 0.5rem;
    margin-left: 5px;
}

.device-controls .btn:first-child {
    margin-left: 0;
}

/* 直接通信按钮样式 */
.btn-outline-success {
    border-color: #28a745;
    color: #28a745;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* 自动刷新按钮样式 */
.btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

/* 直接通信选项样式 */
.form-check-label {
    font-size: 0.9rem;
}

.form-check-label i {
    color: #28a745;
} 