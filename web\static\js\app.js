/**
 * ESP32 MCP Wake Web管理界面 JavaScript
 * 与ESP32 mcp_wake HTTP服务器配套使用
 * 
 * Copyright (c) 2025 PonYoung（旷）
 * License: MIT
 */

class ESP32WebManager {
    constructor() {
        this.autoRefresh = false; // 默认关闭自动刷新
        this.refreshInterval = 10000; // 10秒 - 仅用于设备状态监控
        this.refreshTimer = null;
        
        // 任务自动刷新相关
        this.taskAutoRefresh = false; // 默认关闭任务自动刷新
        this.taskRefreshInterval = 5000; // 5秒 - 任务列表刷新间隔
        this.taskRefreshTimer = null;
        
        // 等待DOM完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeAfterDOM();
            });
        } else {
            this.initializeAfterDOM();
        }
    }

    /**
     * DOM加载完成后的初始化
     */
    initializeAfterDOM() {
        this.initializeEventListeners();
        this.updateAutoRefreshButton();
        this.updateTaskAutoRefreshButton();
        
        // 任务自动刷新默认关闭，用户可手动开启
        
        // 初始加载数据
        this.loadTasks();
        this.loadDevices();
        
        // 初始状态检查
        this.checkDeviceStatus();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 设备控制
        const refreshBtn = document.getElementById('refresh-status');
        const autoRefreshBtn = document.getElementById('auto-refresh-toggle');
        const wakeBtn = document.getElementById('wake-now');
        const rebootBtn = document.getElementById('reboot-device');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.checkDeviceStatus());
        }
        
        if (autoRefreshBtn) {
            autoRefreshBtn.addEventListener('click', () => this.toggleAutoRefresh());
        }
        
        if (wakeBtn) {
            wakeBtn.addEventListener('click', () => this.wakeDevice());
        }
        
        if (rebootBtn) {
            rebootBtn.addEventListener('click', () => this.confirmReboot());
        }

        // 任务管理
        const addTimerBtn = document.getElementById('add-timer');
        const refreshTasksBtn = document.getElementById('refresh-tasks');
        const autoRefreshTasksBtn = document.getElementById('auto-refresh-tasks');
        
        if (addTimerBtn) {
            addTimerBtn.addEventListener('click', () => this.addTask());
        }
        
        if (refreshTasksBtn) {
            refreshTasksBtn.addEventListener('click', () => this.loadTasks());
        }
        
        if (autoRefreshTasksBtn) {
            autoRefreshTasksBtn.addEventListener('click', () => this.toggleTaskAutoRefresh());
        }

        // 任务类型切换
        document.querySelectorAll('input[name="timer-type"]').forEach(radio => {
            radio.addEventListener('change', () => this.switchTimerType());
        });

        // 确认模态框
        const confirmBtn = document.getElementById('confirmButton');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.executeConfirmedAction());
        }

        // 设备管理
        const addDeviceBtn = document.getElementById('add-device');
        const refreshDevicesBtn = document.getElementById('refresh-devices');
        const sendInterDeviceBtn = document.getElementById('send-inter-device');
        
        console.log('Setting up event listeners...');
        console.log('Add device button found:', addDeviceBtn);
        
        if (addDeviceBtn) {
            console.log('Binding click event to add device button');
            addDeviceBtn.addEventListener('click', () => {
                console.log('Add device button clicked!');
                this.addDevice();
            });
        } else {
            console.error('Add device button not found!');
        }
        
        if (refreshDevicesBtn) {
            refreshDevicesBtn.addEventListener('click', () => this.loadDevices());
        }
        
        if (sendInterDeviceBtn) {
            sendInterDeviceBtn.addEventListener('click', () => this.sendInterDeviceMessage());
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notificationArea = document.getElementById('notification-area');
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const iconClass = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show`;
        notification.innerHTML = `
            <i class="${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        notificationArea.appendChild(notification);

        // 自动删除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
    }

    /**
     * API请求封装
     */
    async apiRequest(url, options = {}) {
        try {
            options.headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            const response = await fetch(url, options);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * 检查设备状态
     */
    async checkDeviceStatus() {
        const refreshBtn = document.getElementById('refresh-status');
        const deviceStatus = document.getElementById('device-status');
        
        try {
            refreshBtn.classList.add('loading');
            
            const status = await this.apiRequest('/api/esp32/status');
            
            // 更新导航栏中的连接状态
            if (status.connected) {
                deviceStatus.innerHTML = '<i class="fas fa-circle text-success me-1"></i>设备在线';
            } else {
                deviceStatus.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>设备离线';
            }

            // 更新设备信息
            this.updateDeviceInfo(status);
            
        } catch (error) {
            console.error('设备状态检查失败:', error);
            deviceStatus.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>连接失败';
            this.updateDeviceInfo({
                connected: false,
                device_state: 'error',
                message: error.message
            });
        } finally {
            refreshBtn.classList.remove('loading');
        }
    }

    /**
     * 更新设备信息显示
     */
    updateDeviceInfo(status) {
        const deviceState = document.getElementById('device-state');
        const deviceIp = document.getElementById('device-ip');
        const lastUpdate = document.getElementById('last-update');

        // 设备状态
        const stateMap = {
            'idle': { text: '空闲', class: 'status-idle' },
            'listening': { text: '监听中', class: 'status-listening' },
            'speaking': { text: '讲话中', class: 'status-speaking' },
            'connecting': { text: '连接中', class: 'status-listening' },
            'activating': { text: '激活中', class: 'status-listening' },
            'offline': { text: '离线', class: 'status-offline' },
            'error': { text: '错误', class: 'status-error' },
            'timeout': { text: '超时', class: 'status-error' }
        };

        const state = stateMap[status.device_state] || { text: '未知', class: 'status-offline' };
        if (deviceState) {
            deviceState.textContent = state.text;
            deviceState.className = `status-badge ${state.class}`;
        }

        // IP地址
        if (deviceIp) {
            deviceIp.textContent = status.ip_address || (status.connected ? '已连接' : '未连接');
        }

        // 更新时间
        if (lastUpdate) {
            lastUpdate.textContent = new Date().toLocaleTimeString();
        }

        // 输出调试信息
        console.log('设备状态更新:', {
            device_state: status.device_state,
            connected: status.connected,
            timestamp: status.timestamp,
            can_wake: status.can_wake
        });
    }

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh() {
        this.autoRefresh = !this.autoRefresh;
        
        this.updateAutoRefreshButton();
        
        if (this.autoRefresh) {
            this.startAutoRefresh();
            this.showNotification('✅ 自动刷新已开启', 'success', 2000);
        } else {
            this.stopAutoRefresh();
            this.showNotification('⏸️ 自动刷新已暂停', 'warning', 2000);
        }
    }

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            if (this.autoRefresh) {
                this.checkDeviceStatus();
            }
        }, this.refreshInterval);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    /**
     * 发送消息到设备
     */
    async wakeDevice() {
        const message = document.getElementById('wake-message').value.trim();
        const force = document.getElementById('force-wake').checked;
        const button = document.getElementById('wake-now');

        if (!message) {
            this.showNotification('请输入消息内容', 'warning');
            return;
        }

        // 如果勾选了直接回复，使用逗号分隔符格式
        let finalMessage = message;
        if (force) {
            finalMessage = `请你只回复,,,${message},,,之间的内容，不要添加任何其他文字。示例：如果我说,,,你好世界,,,，你应该只回复：你好世界`;
        }

        try {
            button.classList.add('loading');
            
            const result = await this.apiRequest('/api/esp32/wake', {
                method: 'POST',
                body: JSON.stringify({ message: finalMessage, force })
            });

            if (result.success) {
                this.showNotification(`✅ 消息发送成功: ${force ? '(直接回复模式)' : ''} ${message}`, 'success');
                // 立即刷新设备状态
                setTimeout(() => this.checkDeviceStatus(), 1000);
            } else {
                this.showNotification(`❌ 消息发送失败: ${result.message}`, 'error');
            }
            
        } catch (error) {
            this.showNotification(`❌ 消息发送失败: ${error.message}`, 'error');
        } finally {
            button.classList.remove('loading');
        }
    }

    /**
     * 确认重启设备
     */
    confirmReboot() {
        this.showConfirmModal(
            '确认重启设备',
            '设备将会重启，这将中断当前所有操作。确定要继续吗？',
            () => this.rebootDevice()
        );
    }

    /**
     * 重启设备
     */
    async rebootDevice() {
        try {
            const result = await this.apiRequest('/api/esp32/reboot', {
                method: 'POST'
            });

            if (result.success) {
                this.showNotification('✅ 重启指令已发送，设备将在1秒后重启', 'success');
            } else {
                this.showNotification(`❌ 重启失败: ${result.message}`, 'error');
            }
            
        } catch (error) {
            this.showNotification(`❌ 重启失败: ${error.message}`, 'error');
        }
    }

    /**
     * 切换定时器类型
     */
    switchTimerType() {
        const absolutePanel = document.getElementById('absolute-time-panel');
        const relativePanel = document.getElementById('relative-time-panel');
        const taskOptions = document.getElementById('task-options');
        const selectedType = document.querySelector('input[name="timer-type"]:checked').value;

        if (selectedType === 'absolute') {
            absolutePanel.style.display = 'block';
            relativePanel.style.display = 'none';
            taskOptions.style.display = 'block';
        } else {
            absolutePanel.style.display = 'none';
            relativePanel.style.display = 'block';
            taskOptions.style.display = 'none'; // 相对时间任务默认是一次性的
        }
    }

    /**
     * 添加任务
     */
    async addTask() {
        const selectedType = document.querySelector('input[name="timer-type"]:checked').value;
        const message = document.getElementById('timer-message').value.trim();
        const button = document.getElementById('add-timer');

        if (!message) {
            this.showNotification('请输入消息内容', 'warning');
            return;
        }

        let taskData = { message };

        try {
            button.classList.add('loading');

            if (selectedType === 'absolute') {
                // 绝对时间任务
                const hour = parseInt(document.getElementById('abs-hour').value);
                const minute = parseInt(document.getElementById('abs-minute').value);
                const second = parseInt(document.getElementById('abs-second').value);
                const oneTime = document.getElementById('one-time-task').checked;

                const repeatDays = [];
                for (let i = 0; i <= 6; i++) {
                    if (document.getElementById(`day-${i}`).checked) {
                        repeatDays.push(i);
                    }
                }

                if (repeatDays.length === 0 && !oneTime) {
                    this.showNotification('请选择重复日期或设置为一次性任务', 'warning');
                    return;
                }

                taskData = {
                    ...taskData,
                    hour,
                    minute,
                    second,
                    repeat_days: repeatDays,
                    one_time: oneTime
                };

                const result = await this.apiRequest('/api/timers', {
                    method: 'POST',
                    body: JSON.stringify(taskData)
                });

                this.showNotification(result.message, 'success');
                console.log('绝对时间任务添加成功，正在重新加载任务列表...');
                
                // 立即重新加载任务列表
                await this.loadTasks();
                
                // 重置表单
                this.resetTaskForm();

            } else {
                // 相对时间任务
                const hours = parseInt(document.getElementById('rel-hours').value) || 0;
                const minutes = parseInt(document.getElementById('rel-minutes').value) || 0;
                const seconds = parseInt(document.getElementById('rel-seconds').value) || 0;

                if (hours === 0 && minutes === 0 && seconds === 0) {
                    this.showNotification('请设置有效的时间间隔', 'warning');
                    return;
                }

                taskData = {
                    ...taskData,
                    hours,
                    minutes,
                    seconds
                };

                const result = await this.apiRequest('/api/timers/relative', {
                    method: 'POST',
                    body: JSON.stringify(taskData)
                });

                this.showNotification(result.message, 'success');
                console.log('相对时间任务添加成功，正在重新加载任务列表...');
                
                // 立即重新加载任务列表
                await this.loadTasks();
                
                // 重置表单
                this.resetTaskForm();
            }

        } catch (error) {
            this.showNotification(`❌ 添加任务失败: ${error.message}`, 'error');
        } finally {
            button.classList.remove('loading');
        }
    }

    /**
     * 重置任务表单
     */
    resetTaskForm() {
        document.getElementById('timer-message').value = '定时提醒';
        document.getElementById('one-time-task').checked = false;
        
        // 重置相对时间
        document.getElementById('rel-hours').value = 0;
        document.getElementById('rel-minutes').value = 5;
        document.getElementById('rel-seconds').value = 0;
    }

    /**
     * 加载任务列表
     */
    async loadTasks() {
        const tasksList = document.getElementById('tasks-list');
        const taskCount = document.getElementById('task-count');
        const refreshBtn = document.getElementById('refresh-tasks');

        try {
            if (refreshBtn) {
                refreshBtn.classList.add('loading');
            }
            
            const result = await this.apiRequest('/api/timers');
            const tasks = result.tasks || [];

            if (taskCount) {
                taskCount.textContent = `${tasks.length} 个任务`;
            }

            if (!tasksList) {
                console.error('任务列表元素未找到');
                return;
            }

            if (tasks.length === 0) {
                tasksList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <p class="mb-0">暂无定时任务</p>
                        <small class="text-muted">添加您的第一个定时任务开始使用</small>
                    </div>
                `;
                return;
            }

            // 按启用状态和下次执行时间排序
            tasks.sort((a, b) => {
                if (a.enabled !== b.enabled) {
                    return b.enabled - a.enabled; // 启用的在前
                }
                if (a.next_execution && b.next_execution) {
                    return new Date(a.next_execution) - new Date(b.next_execution);
                }
                return 0;
            });

            tasksList.innerHTML = tasks.map(task => this.renderTaskItem(task)).join('');
            console.log(`任务列表已更新，共 ${tasks.length} 个任务`);

        } catch (error) {
            console.error('加载任务失败:', error);
            this.showNotification(`❌ 加载任务失败: ${error.message}`, 'error');
            if (tasksList) {
                tasksList.innerHTML = `
                    <div class="text-center text-danger py-4">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>加载任务失败</p>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('loading');
            }
        }
    }

    /**
     * 渲染任务项
     */
    renderTaskItem(task) {
        const typeClass = task.type === 'absolute' ? 'task-type-absolute' : 'task-type-relative';
        const disabledClass = task.enabled ? '' : 'disabled';
        
        // 时间信息
        let timeInfo = '';
        if (task.type === 'absolute') {
            const time = task.time_config.time;
            const days = task.time_config.repeat_days || [];
            const dayNames = ['日', '一', '二', '三', '四', '五', '六'];
            const dayStr = days.map(d => dayNames[d]).join(' ');
            timeInfo = `每 ${dayStr} ${time}`;
        } else {
            const { hours = 0, minutes = 0, seconds = 0 } = task.time_config;
            const parts = [];
            if (hours > 0) parts.push(`${hours}小时`);
            if (minutes > 0) parts.push(`${minutes}分钟`);
            if (seconds > 0) parts.push(`${seconds}秒`);
            timeInfo = `${parts.join(' ')}后`;
        }

        // 下次执行时间
        let nextExecution = '';
        if (task.next_execution && task.enabled) {
            const nextTime = new Date(task.next_execution);
            nextExecution = `下次执行: ${nextTime.toLocaleString()}`;
        }

        // 最后执行时间
        let lastExecution = '';
        if (task.last_executed) {
            const lastTime = new Date(task.last_executed);
            lastExecution = `最后执行: ${lastTime.toLocaleString()}`;
        }

        return `
            <div class="task-item ${typeClass} ${disabledClass}" data-task-id="${task.id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-2">${this.escapeHtml(task.message)}</h6>
                            <span class="badge ${task.type === 'absolute' ? 'bg-primary' : 'bg-success'}">
                                ${task.type === 'absolute' ? '绝对时间' : '相对时间'}
                            </span>
                            ${task.one_time ? '<span class="badge bg-warning text-dark ms-1">一次性</span>' : ''}
                        </div>
                        <div class="task-meta">
                            <div><i class="fas fa-clock me-1"></i>${timeInfo}</div>
                            ${nextExecution ? `<div class="text-success"><i class="fas fa-play me-1"></i>${nextExecution}</div>` : ''}
                            ${lastExecution ? `<div class="text-muted"><i class="fas fa-history me-1"></i>${lastExecution}</div>` : ''}
                        </div>
                    </div>
                    <div class="task-controls">
                        <button class="btn btn-sm ${task.enabled ? 'btn-outline-warning' : 'btn-outline-success'}" 
                                onclick="esp32Manager.toggleTask('${task.id}', ${!task.enabled})"
                                title="${task.enabled ? '禁用任务' : '启用任务'}">
                            <i class="fas ${task.enabled ? 'fa-pause' : 'fa-play'}"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="esp32Manager.confirmDeleteTask('${task.id}')"
                                title="删除任务">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 切换任务状态
     */
    async toggleTask(taskId, enabled) {
        try {
            const result = await this.apiRequest(`/api/timers/${taskId}`, {
                method: 'PUT',
                body: JSON.stringify({ enabled })
            });

            this.showNotification(result.message, 'success');
            console.log('任务删除成功，正在重新加载任务列表...');
            // 立即重新加载任务列表
            await this.loadTasks();

        } catch (error) {
            this.showNotification(`❌ 更新任务失败: ${error.message}`, 'error');
        }
    }

    /**
     * 确认删除任务
     */
    confirmDeleteTask(taskId) {
        this.showConfirmModal(
            '确认删除任务',
            '删除后将无法恢复，确定要删除这个定时任务吗？',
            () => this.deleteTask(taskId)
        );
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId) {
        try {
            const result = await this.apiRequest(`/api/timers/${taskId}`, {
                method: 'DELETE'
            });

            this.showNotification(result.message, 'success');
            // 立即重新加载任务列表
            await this.loadTasks();

        } catch (error) {
            this.showNotification(`❌ 删除任务失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示确认模态框
     */
    showConfirmModal(title, message, onConfirm) {
        const modal = document.getElementById('confirmModal');
        const titleElement = modal.querySelector('.modal-title');
        const bodyElement = document.getElementById('confirmModalBody');
        
        titleElement.innerHTML = `<i class="fas fa-question-circle text-warning me-2"></i>${title}`;
        bodyElement.textContent = message;
        
        this.confirmAction = onConfirm;
        
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    /**
     * 执行确认的操作
     */
    executeConfirmedAction() {
        if (this.confirmAction) {
            this.confirmAction();
            this.confirmAction = null;
        }
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
        modal.hide();
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 更新自动刷新按钮状态
     */
    updateAutoRefreshButton() {
        const button = document.getElementById('auto-refresh-toggle');
        if (button) {
            if (this.autoRefresh) {
                button.innerHTML = '<i class="fas fa-pause"></i> 暂停自动';
                button.className = 'btn btn-outline-light btn-sm';
            } else {
                button.innerHTML = '<i class="fas fa-play"></i> 开启自动';
                button.className = 'btn btn-outline-warning btn-sm';
            }
        }
    }

    /**
     * 切换任务自动刷新状态
     */
    toggleTaskAutoRefresh() {
        this.taskAutoRefresh = !this.taskAutoRefresh;
        
        this.updateTaskAutoRefreshButton();
        
        if (this.taskAutoRefresh) {
            this.startTaskAutoRefresh();
            this.showNotification('✅ 任务自动刷新已开启', 'success', 2000);
        } else {
            this.stopTaskAutoRefresh();
            this.showNotification('⏸️ 任务自动刷新已暂停', 'warning', 2000);
        }
    }

    /**
     * 启动任务自动刷新
     */
    startTaskAutoRefresh() {
        if (this.taskRefreshTimer) {
            clearInterval(this.taskRefreshTimer);
        }
        
        this.taskRefreshTimer = setInterval(() => {
            if (this.taskAutoRefresh) {
                this.loadTasks();
            }
        }, this.taskRefreshInterval);
    }

    /**
     * 停止任务自动刷新
     */
    stopTaskAutoRefresh() {
        if (this.taskRefreshTimer) {
            clearInterval(this.taskRefreshTimer);
            this.taskRefreshTimer = null;
        }
    }

    /**
     * 更新任务自动刷新按钮状态
     */
    updateTaskAutoRefreshButton() {
        const button = document.getElementById('auto-refresh-tasks');
        if (button) {
            if (this.taskAutoRefresh) {
                button.innerHTML = '<i class="fas fa-pause"></i>';
                button.className = 'btn btn-outline-warning btn-sm ms-2';
                button.title = '暂停自动刷新任务';
            } else {
                button.innerHTML = '<i class="fas fa-play"></i>';
                button.className = 'btn btn-outline-success btn-sm ms-2';
                button.title = '开启自动刷新任务';
            }
        }
    }

    // ==================== 设备管理方法 ====================

    /**
     * 加载设备列表
     */
    async loadDevices() {
        const devicesList = document.getElementById('devices-list');
        const deviceCount = document.getElementById('device-count');
        const refreshBtn = document.getElementById('refresh-devices');

        try {
            if (refreshBtn) {
                refreshBtn.classList.add('loading');
            }
            
            const result = await this.apiRequest('/api/devices');
            const devices = result.devices || {};
            const deviceArray = Object.values(devices);

            if (deviceCount) {
                deviceCount.textContent = `${deviceArray.length} 个设备`;
            }

            if (!devicesList) {
                console.error('设备列表元素未找到');
                return;
            }

            if (deviceArray.length === 0) {
                devicesList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-microchip"></i>
                        <p class="mb-0">暂无设备</p>
                        <small class="text-muted">添加您的第一个设备开始使用</small>
                    </div>
                `;
            } else {
                devicesList.innerHTML = deviceArray.map(device => this.renderDeviceItem(device)).join('');
                
                // 添加编辑设备名称的事件监听器
                this.attachEditDeviceNameListeners();
            }

            console.log(`设备列表已更新，共 ${deviceArray.length} 个设备`);

            // 更新设备选择下拉框
            this.updateDeviceSelectors(deviceArray);

        } catch (error) {
            console.error('加载设备失败:', error);
            this.showNotification(`❌ 加载设备失败: ${error.message}`, 'error');
            if (devicesList) {
                devicesList.innerHTML = `
                    <div class="text-center text-danger py-4">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>加载设备失败</p>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('loading');
            }
        }
    }

    /**
     * 渲染设备项
     */
    renderDeviceItem(device) {
        const onlineClass = device.online ? 'device-online' : 'device-offline';
        const statusText = device.online ? '在线' : '离线';
        const statusIcon = device.online ? 'fa-circle text-success' : 'fa-circle text-danger';
        
        const stateMap = {
            'idle': { text: '空闲', class: 'status-idle' },
            'listening': { text: '监听中', class: 'status-listening' },
            'speaking': { text: '讲话中', class: 'status-speaking' },
            'connecting': { text: '连接中', class: 'status-listening' },
            'activating': { text: '激活中', class: 'status-listening' },
            'offline': { text: '离线', class: 'status-offline' },
            'error': { text: '错误', class: 'status-error' },
            'timeout': { text: '超时', class: 'status-error' }
        };

        const deviceState = device.status?.device_state || 'offline';
        const state = stateMap[deviceState] || { text: '未知', class: 'status-offline' };

        return `
            <div class="device-item ${onlineClass}" data-device-id="${device.device_id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-2 device-name" data-device-id="${device.device_id}">${this.escapeHtml(device.name)}</h6>
                            <button class="btn btn-sm btn-outline-secondary me-2 edit-device-name" 
                                    data-device-id="${device.device_id}" 
                                    data-device-name="${this.escapeHtml(device.name)}"
                                    title="编辑设备名称">
                                <i class="fas fa-edit"></i>
                            </button>
                            <span class="badge bg-secondary">${device.device_id}</span>
                            <span class="badge ${state.class} ms-1">${state.text}</span>
                        </div>
                        <div class="device-meta">
                            <div><i class="fas ${statusIcon} me-1"></i>${statusText}</div>
                            <div><i class="fas fa-network-wired me-1"></i>${device.ip}:${device.port}</div>
                            ${device.status?.timestamp ? `<div class="text-muted"><i class="fas fa-clock me-1"></i>最后更新: ${new Date(device.status.timestamp).toLocaleString()}</div>` : ''}
                        </div>
                    </div>
                    <div class="device-controls">
                        <button class="btn btn-sm btn-outline-primary" 
                                onclick="esp32Manager.sendToDevice('${device.device_id}')"
                                title="通过服务器发送消息">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" 
                                onclick="esp32Manager.sendDirectToDevice('${device.device_id}')"
                                title="直接发送消息">
                            <i class="fas fa-bolt"></i>
                        </button>
                        ${device.device_id !== 'default' ? `
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="esp32Manager.confirmDeleteDevice('${device.device_id}')"
                                title="删除设备">
                            <i class="fas fa-trash"></i>
                        </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 更新设备选择下拉框
     */
    updateDeviceSelectors(devices) {
        const fromDeviceSelect = document.getElementById('from-device');
        const toDeviceSelect = document.getElementById('to-device');

        if (fromDeviceSelect && toDeviceSelect) {
            const options = devices.map(device => 
                `<option value="${device.device_id}">${device.name} (${device.device_id})</option>`
            ).join('');

            fromDeviceSelect.innerHTML = '<option value="">选择源设备...</option>' + options;
            toDeviceSelect.innerHTML = '<option value="">选择目标设备...</option>' + options;
        }
    }

    /**
     * 添加新设备
     */
    async addDevice() {
        console.log('=== addDevice function called ===');
        
        const deviceIdElement = document.getElementById('device-id');
        const deviceNameElement = document.getElementById('device-name');
        const deviceIpElement = document.getElementById('add-device-ip');
        const devicePortElement = document.getElementById('device-port');
        const button = document.getElementById('add-device');

        console.log('Elements found:', { 
            deviceIdElement: !!deviceIdElement, 
            deviceNameElement: !!deviceNameElement, 
            deviceIpElement: !!deviceIpElement, 
            devicePortElement: !!devicePortElement,
            button: !!button 
        });

        // Debug element types and values
        console.log('Element details:', {
            deviceIdElement: { tagName: deviceIdElement?.tagName, type: deviceIdElement?.type, value: deviceIdElement?.value },
            deviceNameElement: { tagName: deviceNameElement?.tagName, type: deviceNameElement?.type, value: deviceNameElement?.value },
            deviceIpElement: { tagName: deviceIpElement?.tagName, type: deviceIpElement?.type, value: deviceIpElement?.value },
            devicePortElement: { tagName: devicePortElement?.tagName, type: devicePortElement?.type, value: devicePortElement?.value }
        });

        if (!deviceIdElement || !deviceNameElement || !deviceIpElement || !devicePortElement) {
            console.error('Some form elements not found!');
            this.showNotification('表单元素未找到，请刷新页面重试', 'error');
            return;
        }
        
        // 实时获取当前输入框的值
        console.log('Real-time element values:', {
            deviceId: document.getElementById('device-id')?.value,
            deviceName: document.getElementById('device-name')?.value,
            deviceIp: document.getElementById('device-ip')?.value,
            devicePort: document.getElementById('device-port')?.value
        });
        
        // Safe value extraction with null checks
        const deviceId = (deviceIdElement.value || '').trim();
        const deviceName = (deviceNameElement.value || '').trim();
        const deviceIp = (deviceIpElement.value || '').trim();
        const devicePort = parseInt(devicePortElement.value) || 8081;

        console.log('Input values:', { deviceId, deviceName, deviceIp, devicePort });
        
        if (!deviceId || !deviceName || !deviceIp) {
            console.log('Validation failed: missing required fields');
            this.showNotification('请填写完整的设备信息', 'warning');
            return;
        }

        console.log('Validation passed, starting API request...');
        
        try {
            button.classList.add('loading');
            console.log('Button loading state added');
            
            const requestData = {
                device_id: deviceId,
                name: deviceName,
                ip: deviceIp,
                port: devicePort
            };
            console.log('Request data prepared:', requestData);
            
            console.log('Making API request to /api/devices...');
            const result = await this.apiRequest('/api/devices', {
                method: 'POST',
                body: JSON.stringify(requestData)
            });
            console.log('API request completed, result:', result);

            if (result.success) {
                console.log('Device added successfully');
                this.showNotification(`✅ 设备添加成功: ${deviceName}`, 'success');
                // 立即重新加载设备列表
                await this.loadDevices();
                this.resetDeviceForm();
            } else {
                console.log('Device addition failed:', result.message);
                this.showNotification(`❌ 设备添加失败: ${result.message}`, 'error');
            }
            
        } catch (error) {
            console.error('Error in addDevice:', error);
            this.showNotification(`❌ 设备添加失败: ${error.message}`, 'error');
        } finally {
            button.classList.remove('loading');
            console.log('Button loading state removed');
            console.log('=== addDevice function completed ===');
        }
    }

    /**
     * 重置设备表单
     */
    resetDeviceForm() {
        document.getElementById('device-id').value = '';
        document.getElementById('device-name').value = '';
        document.getElementById('device-ip').value = '';
        document.getElementById('device-port').value = '8081';
    }

    /**
     * 发送设备间消息
     */
    async sendInterDeviceMessage() {
        const fromDeviceId = document.getElementById('from-device').value;
        const toDeviceId = document.getElementById('to-device').value;
        const message = document.getElementById('inter-device-message').value.trim();
        const force = document.getElementById('inter-device-force').checked;
        const directCommunication = document.getElementById('direct-communication').checked;
        const button = document.getElementById('send-inter-device');

        if (!toDeviceId || !message) {
            this.showNotification('请选择目标设备并输入消息内容', 'warning');
            return;
        }

        if (!directCommunication && !fromDeviceId) {
            this.showNotification('中转模式需要选择源设备', 'warning');
            return;
        }

        if (!directCommunication && fromDeviceId === toDeviceId) {
            this.showNotification('源设备和目标设备不能相同', 'warning');
            return;
        }

        // 如果勾选了直接回复，使用逗号分隔符格式
        let finalMessage = message;
        if (force) {
            finalMessage = `请你只回复,,,${message},,,之间的内容，不要添加任何其他文字。示例：如果我说,,,你好世界,,,，你应该只回复：你好世界`;
        }

        try {
            button.classList.add('loading');
            
            if (directCommunication) {
                // 直接通信模式
                await this.sendDirectMessage(toDeviceId, finalMessage, force);
            } else {
                // 中转模式
                const result = await this.apiRequest('/api/devices/send-between', {
                    method: 'POST',
                    body: JSON.stringify({
                        from_device_id: fromDeviceId,
                        to_device_id: toDeviceId,
                        message: finalMessage,
                        force: force
                    })
                });

                if (result.success) {
                    this.showNotification(`✅ 设备间消息发送成功: ${force ? '(直接回复模式)' : ''} ${message}`, 'success');
                    document.getElementById('inter-device-message').value = '';
                } else {
                    this.showNotification(`❌ 设备间消息发送失败: ${result.message}`, 'error');
                }
            }
            
        } catch (error) {
            this.showNotification(`❌ 设备间消息发送失败: ${error.message}`, 'error');
        } finally {
            button.classList.remove('loading');
        }
    }

    /**
     * 向指定设备发送消息（通过服务器）
     */
    async sendToDevice(deviceId) {
        const message = prompt('请输入要发送的消息:');
        if (!message) return;

        const force = confirm('是否使用直接回复模式？\n点击"确定"使用直接回复模式，点击"取消"使用普通模式。');

        // 如果勾选了直接回复，使用逗号分隔符格式
        let finalMessage = message;
        if (force) {
            finalMessage = `请你只回复,,,${message},,,之间的内容，不要添加任何其他文字。示例：如果我说,,,你好世界,,,，你应该只回复：你好世界`;
        }

        try {
            const result = await this.apiRequest(`/api/devices/${deviceId}/send`, {
                method: 'POST',
                body: JSON.stringify({
                    message: finalMessage,
                    force: force
                })
            });

            if (result.success) {
                this.showNotification(`✅ 消息发送成功: ${force ? '(直接回复模式)' : ''} ${message}`, 'success');
            } else {
                this.showNotification(`❌ 消息发送失败: ${result.message}`, 'error');
            }

        } catch (error) {
            this.showNotification(`❌ 消息发送失败: ${error.message}`, 'error');
        }
    }

    /**
     * 直接向设备发送消息（不通过服务器）
     */
    async sendDirectToDevice(deviceId) {
        const message = prompt('请输入要直接发送的消息:');
        if (!message) return;

        const force = confirm('是否使用直接回复模式？\n点击"确定"使用直接回复模式，点击"取消"使用普通模式。');

        // 如果勾选了直接回复，使用逗号分隔符格式
        let finalMessage = message;
        if (force) {
            finalMessage = `请你只回复,,,${message},,,之间的内容，不要添加任何其他文字。示例：如果我说,,,你好世界,,,，你应该只回复：你好世界`;
        }

        try {
            await this.sendDirectMessage(deviceId, finalMessage, force);
        } catch (error) {
            this.showNotification(`❌ 直接消息发送失败: ${error.message}`, 'error');
        }
    }

    /**
     * 直接通信核心方法
     */
    async sendDirectMessage(deviceId, message, force) {
        console.log(`=== 开始直接通信 ===`);
        console.log(`设备ID: ${deviceId}, 消息: ${message}, 强制模式: ${force}`);
        
        try {
            // 首先获取设备连接信息
            console.log('步骤1: 获取设备连接信息...');
            const deviceInfo = await this.apiRequest('/api/devices/send-direct', {
                method: 'POST',
                body: JSON.stringify({
                    to_device_id: deviceId,
                    message: message,
                    force: force
                })
            });

            console.log('设备信息获取结果:', deviceInfo);

            if (!deviceInfo.success) {
                throw new Error(deviceInfo.message);
            }

            const targetDevice = deviceInfo.target_device;
            const payload = deviceInfo.payload;

            console.log('目标设备信息:', targetDevice);
            console.log('发送载荷:', payload);

            // 直接向目标设备发送消息
            const directUrl = `${targetDevice.url}/api/wake`;
            console.log(`步骤2: 直接连接设备 URL: ${directUrl}`);
            
            this.showNotification(`🔗 正在直接连接设备: ${targetDevice.name} (${targetDevice.ip}:${targetDevice.port})`, 'info');

            // 使用AbortController实现超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                console.log('请求超时，已取消');
            }, 15000); // 15秒超时

            console.log('步骤3: 发送HTTP请求...');
            const response = await fetch(directUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    // 尝试处理CORS问题
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type'
                },
                body: JSON.stringify(payload),
                signal: controller.signal,
                // 添加模式设置，尝试解决CORS问题
                mode: 'cors'
            });

            clearTimeout(timeoutId);
            console.log(`HTTP响应状态: ${response.status} ${response.statusText}`);
            console.log('响应头:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text().catch(() => '无法读取错误响应');
                console.error(`HTTP错误 ${response.status}: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            console.log('步骤4: 解析响应数据...');
            const contentType = response.headers.get('content-type');
            console.log('响应内容类型:', contentType);
            
            let result;
            if (contentType && contentType.includes('application/json')) {
                result = await response.json();
            } else {
                const textResult = await response.text();
                console.log('非JSON响应:', textResult);
                // 尝试解析为JSON，如果失败则构造响应
                try {
                    result = JSON.parse(textResult);
                } catch {
                    result = {
                        success: response.ok,
                        message: textResult || '设备响应成功',
                        response_text: textResult
                    };
                }
            }

            console.log('设备响应结果:', result);

            if (result.success !== false) { // 兼容不同的响应格式
                this.showNotification(`⚡ 直接消息发送成功: ${message}`, 'success');
                const messageInput = document.getElementById('inter-device-message');
                if (messageInput) {
                    messageInput.value = '';
                }
                console.log('=== 直接通信成功 ===');
            } else {
                this.showNotification(`❌ 设备响应失败: ${result.message || '未知错误'}`, 'error');
                console.error('设备响应失败:', result);
            }

        } catch (error) {
            console.error('=== 直接通信失败 ===');
            console.error('错误详情:', error);
            console.error('错误类型:', error.name);
            console.error('错误消息:', error.message);
            
            // 详细的错误分类处理
            if (error.name === 'AbortError') {
                this.showNotification(`❌ 请求超时：设备可能离线或网络连接不稳定`, 'error');
            } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                this.showNotification(`❌ 网络连接失败：无法访问设备 ${deviceId}`, 'error');
            } else if (error.message.includes('CORS')) {
                this.showNotification(`❌ 跨域请求被阻止：请检查设备的CORS设置`, 'error');
            } else if (error.message.includes('HTTP 404')) {
                this.showNotification(`❌ 设备接口不存在：请确认设备支持/api/wake接口`, 'error');
            } else if (error.message.includes('HTTP 500')) {
                this.showNotification(`❌ 设备服务器错误：请检查设备状态`, 'error');
            } else if (error.message.includes('Failed to fetch')) {
                this.showNotification(`❌ 连接被拒绝：请检查设备IP地址(${deviceId})和网络连接`, 'error');
            } else {
                this.showNotification(`❌ 直接通信失败: ${error.message}`, 'error');
            }
            throw error;
        }
    }

    /**
     * 确认删除设备
     */
    confirmDeleteDevice(deviceId) {
        this.showConfirmModal(
            '确认删除设备',
            '删除后将无法恢复，确定要删除这个设备吗？',
            () => this.deleteDevice(deviceId)
        );
    }

    /**
     * 删除设备
     */
    async deleteDevice(deviceId) {
        try {
            const result = await this.apiRequest(`/api/devices/${deviceId}`, {
                method: 'DELETE'
            });

            if (result.success) {
                this.showNotification('✅ 设备删除成功', 'success');
                // 立即重新加载设备列表
                await this.loadDevices();
            } else {
                this.showNotification(`❌ 设备删除失败: ${result.message}`, 'error');
            }

        } catch (error) {
            this.showNotification(`❌ 设备删除失败: ${error.message}`, 'error');
        }
    }

    /**
     * 添加编辑设备名称的事件监听器
     */
    attachEditDeviceNameListeners() {
        const editButtons = document.querySelectorAll('.edit-device-name');
        editButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const deviceId = button.getAttribute('data-device-id');
                const currentName = button.getAttribute('data-device-name');
                this.startEditDeviceName(deviceId, currentName);
            });
        });
    }

    /**
     * 开始编辑设备名称
     */
    startEditDeviceName(deviceId, currentName) {
        const nameElement = document.querySelector(`.device-name[data-device-id="${deviceId}"]`);
        const editButton = document.querySelector(`.edit-device-name[data-device-id="${deviceId}"]`);
        
        if (!nameElement || !editButton) return;

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentName;
        input.className = 'form-control form-control-sm d-inline-block';
        input.style.width = '150px';
        input.style.marginRight = '8px';
        
        // 创建保存和取消按钮
        const saveBtn = document.createElement('button');
        saveBtn.className = 'btn btn-sm btn-success me-1';
        saveBtn.innerHTML = '<i class="fas fa-check"></i>';
        saveBtn.title = '保存';
        
        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'btn btn-sm btn-secondary';
        cancelBtn.innerHTML = '<i class="fas fa-times"></i>';
        cancelBtn.title = '取消';
        
        // 替换名称元素
        const container = document.createElement('div');
        container.className = 'd-flex align-items-center';
        container.appendChild(input);
        container.appendChild(saveBtn);
        container.appendChild(cancelBtn);
        
        nameElement.style.display = 'none';
        editButton.style.display = 'none';
        nameElement.parentNode.insertBefore(container, nameElement);
        
        // 聚焦输入框并选中文本
        input.focus();
        input.select();
        
        // 保存函数
        const saveEdit = async () => {
            const newName = input.value.trim();
            if (!newName) {
                this.showNotification('设备名称不能为空', 'warning');
                return;
            }
            
            if (newName === currentName) {
                cancelEdit();
                return;
            }
            
            try {
                const result = await this.apiRequest(`/api/devices/${deviceId}`, {
                    method: 'PUT',
                    body: JSON.stringify({ name: newName })
                });
                
                if (result.success) {
                    this.showNotification('✅ 设备名称更新成功', 'success');
                    // 重新加载设备列表
                    await this.loadDevices();
                } else {
                    this.showNotification(`❌ 更新失败: ${result.message}`, 'error');
                    cancelEdit();
                }
            } catch (error) {
                this.showNotification(`❌ 更新失败: ${error.message}`, 'error');
                cancelEdit();
            }
        };
        
        // 取消函数
        const cancelEdit = () => {
            container.remove();
            nameElement.style.display = '';
            editButton.style.display = '';
        };
        
        // 事件监听
        saveBtn.addEventListener('click', saveEdit);
        cancelBtn.addEventListener('click', cancelEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
        
        // 点击外部取消编辑
        const handleClickOutside = (e) => {
            if (!container.contains(e.target)) {
                document.removeEventListener('click', handleClickOutside);
                cancelEdit();
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', handleClickOutside);
        }, 100);
    }
}

// 全局变量
let esp32Manager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    esp32Manager = new ESP32WebManager();
    
    console.log('ESP32 MCP Wake Web管理界面已初始化');
});