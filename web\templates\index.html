<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 任务管理 - 小智AI助手</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-microchip me-2"></i>
                    ESP32 任务管理
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item">
                        <span class="nav-link" id="device-status">
                            <i class="fas fa-circle text-warning"></i>
                            连接中...
                        </span>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container">
            <!-- 设备状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card device-status-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-microchip me-2"></i>
                                ESP32 设备状态
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="status-item">
                                        <label>设备状态:</label>
                                        <span id="device-state" class="status-badge status-offline">未知</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="status-item">
                                        <label>IP地址:</label>
                                        <span id="device-ip">加载中...</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="status-item">
                                        <label>最后更新:</label>
                                        <span id="last-update">--</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <button id="refresh-status" class="btn btn-outline-light btn-sm me-2">
                                        <i class="fas fa-sync-alt"></i> 刷新状态
                                    </button>
                                    <button id="auto-refresh-toggle" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-pause"></i> 暂停自动
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速控制面板 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-paper-plane me-2"></i>
                                消息发送控制
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-comment"></i>
                                        </span>
                                        <input type="text" id="wake-message" class="form-control" 
                                               placeholder="输入要发送的消息内容..." value="小智，请帮我...">
                                        <div class="input-group-text">
                                            <input class="form-check-input mt-0" type="checkbox" id="force-wake">
                                            <label class="form-check-label ms-1" for="force-wake" title="勾选后将直接回复您输入的内容，不经过AI处理">直接回复</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <button id="wake-now" class="btn btn-success me-2">
                                        <i class="fas fa-paper-plane"></i> 发送消息
                                    </button>
                                    <button id="reboot-device" class="btn btn-danger">
                                        <i class="fas fa-power-off"></i> 重启设备
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 定时任务管理 -->
            <div class="row">
                <div class="col-md-5">
                    <!-- 添加新任务 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>
                                添加定时任务
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 任务类型选择 -->
                            <div class="mb-3">
                                <label class="form-label">任务类型</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="timer-type" id="absolute-time" value="absolute" checked>
                                    <label class="btn btn-outline-primary" for="absolute-time">绝对时间</label>
                                    
                                    <input type="radio" class="btn-check" name="timer-type" id="relative-time" value="relative">
                                    <label class="btn btn-outline-success" for="relative-time">相对时间</label>
                                </div>
                            </div>

                            <!-- 绝对时间设置 -->
                            <div id="absolute-time-panel">
                                <div class="row mb-3">
                                    <div class="col-4">
                                        <label class="form-label">小时</label>
                                        <input type="number" id="abs-hour" class="form-control" min="0" max="23" value="8">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label">分钟</label>
                                        <input type="number" id="abs-minute" class="form-control" min="0" max="59" value="0">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label">秒</label>
                                        <input type="number" id="abs-second" class="form-control" min="0" max="59" value="0">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">重复日期</label>
                                    <div class="week-selector">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-0" value="0" checked>
                                            <label class="form-check-label" for="day-0">周日</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-1" value="1" checked>
                                            <label class="form-check-label" for="day-1">周一</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-2" value="2" checked>
                                            <label class="form-check-label" for="day-2">周二</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-3" value="3" checked>
                                            <label class="form-check-label" for="day-3">周三</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-4" value="4" checked>
                                            <label class="form-check-label" for="day-4">周四</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-5" value="5" checked>
                                            <label class="form-check-label" for="day-5">周五</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="day-6" value="6" checked>
                                            <label class="form-check-label" for="day-6">周六</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 相对时间设置 -->
                            <div id="relative-time-panel" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-4">
                                        <label class="form-label">小时后</label>
                                        <input type="number" id="rel-hours" class="form-control" min="0" value="0">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label">分钟后</label>
                                        <input type="number" id="rel-minutes" class="form-control" min="0" value="5">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label">秒后</label>
                                        <input type="number" id="rel-seconds" class="form-control" min="0" value="0">
                                    </div>
                                </div>
                            </div>

                            <!-- 唤醒消息 -->
                            <div class="mb-3">
                                <label class="form-label">消息内容</label>
                                <input type="text" id="timer-message" class="form-control" 
                                       placeholder="输入定时发送的消息内容..." value="定时提醒">
                            </div>

                            <!-- 任务选项 -->
                            <div id="task-options" class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="one-time-task">
                                    <label class="form-check-label" for="one-time-task">
                                        一次性任务
                                    </label>
                                </div>
                            </div>

                            <button id="add-timer" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> 添加定时消息
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-7">
                    <!-- 任务列表 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                定时任务列表
                            </h5>
                            <div>
                                <span class="badge bg-info" id="task-count">0 个任务</span>
                                <button id="auto-refresh-tasks" class="btn btn-outline-success btn-sm ms-2" title="开启自动刷新任务">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button id="refresh-tasks" class="btn btn-outline-secondary btn-sm ms-1">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="tasks-list">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-clock fa-3x mb-3"></i>
                                    <p>暂无定时任务</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备管理 -->
            <div class="row mt-4">
                <div class="col-md-5">
                    <!-- 添加新设备 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>
                                添加新设备
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">设备ID</label>
                                <input type="text" id="device-id" class="form-control" 
                                       placeholder="输入设备唯一标识..." value="">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">设备名称</label>
                                <input type="text" id="device-name" class="form-control" 
                                       placeholder="输入设备显示名称..." value="">
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-8">
                                    <label class="form-label">IP地址</label>
                                    <input type="text" id="add-device-ip" class="form-control" 
                                           placeholder="*************" value="">
                                </div>
                                <div class="col-4">
                                    <label class="form-label">端口</label>
                                    <input type="number" id="device-port" class="form-control" 
                                           min="1" max="65535" value="8081">
                                </div>
                            </div>

                            <button id="add-device" class="btn btn-success w-100">
                                <i class="fas fa-plus"></i> 添加设备
                            </button>
                        </div>
                    </div>

                    <!-- 设备间通信 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exchange-alt me-2"></i>
                                设备间通信
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">源设备</label>
                                <select id="from-device" class="form-select">
                                    <option value="">选择源设备...</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">目标设备</label>
                                <select id="to-device" class="form-select">
                                    <option value="">选择目标设备...</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">消息内容</label>
                                <div class="input-group">
                                    <input type="text" id="inter-device-message" class="form-control" 
                                           placeholder="输入要发送的消息..." value="">
                                    <div class="input-group-text">
                                        <input class="form-check-input mt-0" type="checkbox" id="inter-device-force">
                                        <label class="form-check-label ms-1" for="inter-device-force">直接回复</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="direct-communication">
                                    <label class="form-check-label" for="direct-communication">
                                        <i class="fas fa-bolt me-1"></i>直接通信（不通过服务器中转）
                                    </label>
                                </div>
                            </div>

                            <button id="send-inter-device" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane"></i> 发送消息
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-7">
                    <!-- 设备列表 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-microchip me-2"></i>
                                设备列表
                            </h5>
                            <div>
                                <span class="badge bg-info" id="device-count">0 个设备</span>
                                <button id="auto-refresh-devices" class="btn btn-outline-success btn-sm ms-2" title="开启自动刷新设备">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button id="refresh-devices" class="btn btn-outline-secondary btn-sm ms-1">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="devices-list">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-microchip fa-3x mb-3"></i>
                                    <p>暂无设备</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- 消息内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="confirmModalBody">
                    <!-- 确认内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmButton">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知区域 -->
    <div id="notification-area" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>