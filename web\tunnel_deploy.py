#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 MCP Wake Web管理界面 - 内网穿透部署脚本
使用ngrok或其他内网穿透工具快速部署到公网

Copyright (c) 2025 PonYoung（旷）
License: MIT License
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

def check_ngrok():
    """检查ngrok是否安装"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ ngrok未安装")
    print("请访问 https://ngrok.com/download 下载并安装ngrok")
    print("或使用包管理器安装:")
    print("  Windows: choco install ngrok")
    print("  macOS: brew install ngrok")
    print("  Linux: snap install ngrok")
    return False

def start_web_server():
    """启动Web服务器"""
    print("🚀 启动ESP32 MCP Wake Web服务器...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['FLASK_ENV'] = 'production'
    env['ESP32_IP'] = input("请输入ESP32设备IP地址 (默认: *************): ") or "*************"
    
    # 启动服务器
    try:
        process = subprocess.Popen([
            sys.executable, 'wsgi.py'
        ], env=env)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get('http://localhost:5000/api/health', timeout=5)
            if response.status_code == 200:
                print("✅ Web服务器启动成功")
                return process
        except requests.exceptions.RequestException:
            pass
        
        print("❌ Web服务器启动失败")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return None

def start_ngrok():
    """启动ngrok隧道"""
    print("🌐 启动ngrok隧道...")
    
    try:
        # 启动ngrok
        process = subprocess.Popen([
            'ngrok', 'http', '5000', '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待ngrok启动
        time.sleep(3)
        
        # 获取公网URL
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                tunnels = response.json()['tunnels']
                if tunnels:
                    public_url = tunnels[0]['public_url']
                    print(f"✅ ngrok隧道启动成功")
                    print(f"🌐 公网访问地址: {public_url}")
                    return process, public_url
        except requests.exceptions.RequestException:
            pass
        
        print("❌ 无法获取ngrok公网地址")
        process.terminate()
        return None, None
        
    except Exception as e:
        print(f"❌ 启动ngrok失败: {e}")
        return None, None

def main():
    """主函数"""
    print("🚀 ESP32 MCP Wake Web - 内网穿透部署")
    print("=" * 50)
    
    # 检查当前目录
    if not Path('wsgi.py').exists():
        print("❌ 请在web目录下运行此脚本")
        sys.exit(1)
    
    # 检查ngrok
    if not check_ngrok():
        sys.exit(1)
    
    # 启动Web服务器
    web_process = start_web_server()
    if not web_process:
        sys.exit(1)
    
    try:
        # 启动ngrok
        ngrok_process, public_url = start_ngrok()
        if not ngrok_process:
            web_process.terminate()
            sys.exit(1)
        
        print("\n" + "=" * 50)
        print("🎉 部署成功！")
        print(f"🌐 公网访问地址: {public_url}")
        print("📱 您可以在任何地方访问ESP32设备管理界面")
        print("\n⚠️  注意事项:")
        print("1. 保持此终端窗口打开")
        print("2. ngrok免费版有连接数限制")
        print("3. 每次重启URL会变化")
        print("4. 生产环境建议使用专业部署方案")
        print("\n按Ctrl+C停止服务")
        print("=" * 50)
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 正在停止服务...")
            
    finally:
        # 清理进程
        if 'ngrok_process' in locals() and ngrok_process:
            ngrok_process.terminate()
        if web_process:
            web_process.terminate()
        
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
