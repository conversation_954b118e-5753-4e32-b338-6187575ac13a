#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 MCP Wake Web管理界面 - WSGI入口文件
用于生产环境部署 (Gunicorn, uWSGI等)

Copyright (c) 2025 PonYoung（旷）
License: MIT License
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('FLASK_ENV', 'production')

# 导入Flask应用
from server import app

# 配置生产环境
if __name__ != "__main__":
    # 生产环境配置
    import logging
    from logging.handlers import RotatingFileHandler
    
    # 配置日志
    if not app.debug:
        # 文件日志
        file_handler = RotatingFileHandler(
            'logs/esp32_web.log', 
            maxBytes=10240000, 
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('ESP32 MCP Wake Web 启动')

if __name__ == "__main__":
    # 开发环境直接运行
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
