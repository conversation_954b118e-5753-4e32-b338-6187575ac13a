# ESP32 MCP Wake Web服务器公网部署指南

## 🌐 为什么要部署到公网？

当前系统使用硬编码的局域网IP地址 `http://************:5000`，存在以下问题：
- IP地址经常变化，需要频繁修改代码
- 只能在同一局域网内使用
- 设备移动到其他网络时无法工作

部署到公网后的优势：
- ✅ 固定的域名访问地址
- ✅ 支持远程管理和监控
- ✅ 多个网络环境下的设备都能连接
- ✅ 更好的稳定性和可用性

## 🚀 部署方案

### 方案1：免费云平台部署

#### 1.1 Railway (推荐)
```bash
# 1. 安装Railway CLI
npm install -g @railway/cli

# 2. 登录Railway
railway login

# 3. 在项目根目录创建railway.json
echo '{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "cd web && python start.py --host 0.0.0.0 --port $PORT"
  }
}' > railway.json

# 4. 部署
railway up
```

#### 1.2 Render
1. 连接GitHub仓库到Render
2. 设置构建命令：`cd web && pip install -r requirements.txt`
3. 设置启动命令：`cd web && python start.py --host 0.0.0.0 --port $PORT`

#### 1.3 Heroku
```bash
# 1. 创建Procfile
echo "web: cd web && python start.py --host 0.0.0.0 --port \$PORT" > Procfile

# 2. 创建requirements.txt
cd web
pip freeze > requirements.txt

# 3. 部署到Heroku
heroku create your-app-name
git push heroku main
```

### 方案2：VPS部署

#### 2.1 购买VPS
推荐平台：
- 阿里云ECS (国内)
- 腾讯云CVM (国内)
- DigitalOcean (国外)
- Vultr (国外)

#### 2.2 部署步骤
```bash
# 1. 连接VPS
ssh root@your-server-ip

# 2. 安装Python和依赖
apt update
apt install python3 python3-pip git -y

# 3. 克隆项目
git clone https://github.com/your-username/testawake.git
cd testawake/web

# 4. 安装依赖
pip3 install -r requirements.txt

# 5. 使用systemd创建服务
sudo tee /etc/systemd/system/esp32-web.service > /dev/null <<EOF
[Unit]
Description=ESP32 MCP Wake Web Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/testawake/web
ExecStart=/usr/bin/python3 start.py --host 0.0.0.0 --port 5000
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 6. 启动服务
sudo systemctl enable esp32-web
sudo systemctl start esp32-web
```

### 方案3：内网穿透

#### 3.1 使用ngrok
```bash
# 1. 下载并安装ngrok
# 2. 启动本地服务器
cd web && python start.py

# 3. 在另一个终端启动ngrok
ngrok http 5000

# 4. 获取公网地址，如：https://abc123.ngrok.io
```

#### 3.2 使用frp
```bash
# 1. 在VPS上部署frps服务端
# 2. 在本地运行frpc客户端
# 3. 配置端口映射
```

## 🔧 ESP32配置修改

### 方法1：修改配置文件 (推荐)
编辑 `main/boards/bread-compact-wifi/web_config.json`：
```json
{
  "web_server_url": "https://your-domain.com"
}
```

### 方法2：修改代码
编辑 `main/boards/bread-compact-wifi/mcp_wake.cc` 第729行：
```cpp
web_server_url = "https://your-domain.com"; // 替换为您的公网地址
```

## 🛡️ 安全配置

### 1. 添加API密钥认证
在Web服务器中添加API密钥验证：
```python
# web/server.py
API_KEY = "your-secret-api-key"

@app.before_request
def verify_api_key():
    if request.endpoint and request.endpoint.startswith('api'):
        key = request.headers.get('X-API-Key')
        if key != API_KEY:
            return jsonify({'error': 'Unauthorized'}), 401
```

### 2. 使用HTTPS
- 申请SSL证书（Let's Encrypt免费）
- 配置Nginx反向代理
- 强制HTTPS重定向

### 3. 防火墙配置
```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

## 📝 部署后的配置示例

### 成功部署后的地址示例：
- Railway: `https://testawake-production.up.railway.app`
- Render: `https://testawake.onrender.com`
- Heroku: `https://testawake.herokuapp.com`
- 自定义域名: `https://esp32.yourdomain.com`

### ESP32配置示例：
```json
{
  "web_server_url": "https://testawake-production.up.railway.app"
}
```

## 🧪 测试验证

部署完成后，测试以下功能：
1. 访问Web管理界面：`https://your-domain.com`
2. API接口测试：`https://your-domain.com/api/devices`
3. ESP32设备连接测试
4. 设备间通信测试

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 服务器日志
2. ESP32串口输出
3. 网络连接状态
4. 防火墙设置

建议先使用免费的Railway或Render进行测试，确认功能正常后再考虑VPS部署。
