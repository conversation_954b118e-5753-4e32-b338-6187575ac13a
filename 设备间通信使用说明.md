# ESP32设备间通信系统使用说明

## 🎯 功能概述

本系统实现了ESP32设备间的智能通信功能，支持语音控制、直接通信和Web管理界面。

### 核心功能
1. **自动刷新**: 任务列表和设备列表支持自动刷新
2. **直接通信**: 设备可以直接向其他设备发送消息，无需服务器中转
3. **语音控制**: 支持自然语言语音命令控制设备间通信
4. **MCP工具**: 提供完整的设备通信MCP工具
5. **Web界面**: 用户友好的管理界面

## 🚀 快速开始

### 1. 启动Web服务器
```bash
cd web
python server.py
```

### 2. 确保ESP32设备在线
- 检查设备IP地址和端口
- 确保设备连接到同一网络

### 3. 访问Web界面
打开浏览器访问: `http://localhost:5000`

## 🎤 语音控制使用

### 支持的语音命令格式

1. **告诉格式**
   - "告诉设备2今天下午去开会"
   - "告诉客厅设备播放音乐"

2. **向...发送格式**
   - "向卧室设备发送关灯"
   - "向厨房设备发送开始做饭"

3. **给...发消息格式**
   - "给设备2发消息准备开会"
   - "给客厅设备发消息调低音量"

4. **对...说格式**
   - "对卧室设备说晚安"
   - "对厨房设备说开始烹饪"

### 语音控制流程

1. **用户对设备1说**: "告诉设备2今天下午去开会"
2. **设备1处理**: 
   - 接收语音输入
   - 解析命令（目标设备：设备2，消息：今天下午去开会）
   - 调用设备间通信功能
3. **直接通信**: 设备1直接向设备2发送消息
4. **设备2响应**: 播放"今天下午去开会"

## 🌐 Web界面使用

### 自动刷新功能

1. **任务列表自动刷新**
   - 点击任务列表旁的播放按钮开启自动刷新
   - 默认每15秒刷新一次
   - 点击暂停按钮停止自动刷新

2. **设备列表自动刷新**
   - 点击设备列表旁的播放按钮开启自动刷新
   - 默认每20秒刷新一次
   - 实时显示设备在线状态

### 设备间通信

1. **通过服务器中转**
   - 选择源设备和目标设备
   - 输入消息内容
   - 点击"发送消息"

2. **直接通信模式**
   - 勾选"直接通信（不通过服务器中转）"
   - 只需选择目标设备
   - 设备将直接连接发送消息

3. **设备列表操作**
   - 🛩️ 按钮：通过服务器发送消息
   - ⚡ 按钮：直接发送消息
   - 🗑️ 按钮：删除设备

## 🛠️ MCP工具使用

### 命令行工具

```bash
# 列出所有设备
python mcp_device_communication.py list_devices

# 发送消息（通过服务器）
python mcp_device_communication.py send_message "设备2" "今天下午去开会"

# 直接发送消息
python mcp_device_communication.py send_direct "设备2" "今天下午去开会"

# 解析语音命令
python mcp_device_communication.py parse_voice "告诉设备2今天下午去开会"

# 执行语音命令
python mcp_device_communication.py voice_command "告诉设备2今天下午去开会"
```

### API接口

1. **获取设备列表**
   ```
   GET /api/devices
   ```

2. **设备间通信（中转模式）**
   ```
   POST /api/devices/send-between
   {
     "from_device_id": "device1",
     "to_device_id": "device2", 
     "message": "消息内容",
     "force": false
   }
   ```

3. **直接通信信息获取**
   ```
   POST /api/devices/send-direct
   {
     "to_device_id": "device2",
     "message": "消息内容",
     "force": false
   }
   ```

## 🔧 配置说明

### Web服务器配置
- 端口: 5000
- ESP32默认地址: ************:8081

### 自动刷新间隔
- 设备状态: 10秒
- 任务列表: 15秒  
- 设备列表: 20秒

### 设备通信超时
- 直接通信: 10秒
- API请求: 15秒

## 🧪 测试验证

运行完整测试：
```bash
python test_complete_device_communication.py
```

测试内容包括：
- 自动刷新功能
- MCP工具功能
- 直接通信功能
- 语音控制功能
- Web界面功能

## 📋 故障排除

### 常见问题

1. **设备显示离线**
   - 检查设备IP地址和端口
   - 确认设备在同一网络
   - 检查防火墙设置

2. **语音命令无响应**
   - 确认语音命令格式正确
   - 检查目标设备是否存在
   - 查看ESP32设备日志

3. **直接通信失败**
   - 确认目标设备在线
   - 检查网络连接
   - 验证设备API可访问性

4. **自动刷新不工作**
   - 检查浏览器控制台错误
   - 确认Web服务器正常运行
   - 刷新页面重试

### 日志查看

1. **Web服务器日志**
   ```bash
   # 查看服务器控制台输出
   ```

2. **ESP32设备日志**
   ```bash
   # 通过串口监视器查看
   ```

3. **浏览器控制台**
   ```
   F12 -> Console 查看JavaScript错误
   ```

## 🎉 使用示例

### 场景1：智能家居控制
```
用户: "告诉客厅设备播放音乐"
系统: 客厅设备开始播放音乐

用户: "给卧室设备发消息关灯"  
系统: 卧室设备关闭灯光
```

### 场景2：办公室通知
```
用户: "告诉会议室设备会议开始了"
系统: 会议室设备播放通知

用户: "向前台设备发送有客人到访"
系统: 前台设备显示访客提醒
```

### 场景3：多设备协调
```
用户: "通知所有设备准备关机"
系统: 所有设备收到关机准备通知

用户: "告诉厨房设备开始做饭，告诉餐厅设备准备用餐"
系统: 厨房和餐厅设备分别执行相应操作
```

## 📞 技术支持

如有问题，请查看：
1. 项目文档
2. 测试脚本输出
3. 设备日志信息
4. Web界面错误提示

---

**版本**: 1.0.0  
**更新日期**: 2025年1月  
**作者**: PonYoung（旷） 